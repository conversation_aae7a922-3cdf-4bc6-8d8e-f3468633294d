"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, Move, UserPlus, Tag as TagIcon, Trash2, Users } from "lucide-react"
import type { User, Tag, Stage } from "../ticket"
import { Checkbox } from "@/components/ui/checkbox"
import { useState } from "react"
import { fetchTags } from "../tickets"

interface BulkActionsProps {
  selectedCount: number
  onBulkMove: (stageId: string) => void
  onBulkTag: (tagId: string) => void
  onBulkDelete: () => void
  onClearSelection: () => void
  users: User[]
  tags: Tag[]
  stages: Stage[]
}

export function BulkActions({
  selectedCount,
  onBulkMove,
  onBulkTag,
  onBulkDelete,
  onClearSelection,
  users,
  stages,
}: Omit<BulkActionsProps, 'tags'>) {
  const [tags, setTags] = useState<Tag[]>([]);
  const [tagsLoaded, setTagsLoaded] = useState(false);
  const handleTagDropdownOpen = async () => {
    if (!tagsLoaded) {
      const allTags = await fetchTags();
      setTags(allTags);
      setTagsLoaded(true);
    }
  };

  if (selectedCount === 0) return null

  return (
    <div className="flex items-center justify-between bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
      <div className="flex items-center space-x-3">
        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
          {selectedCount} selected
        </Badge>

        <div className="flex items-center space-x-2">
          {/* Move To */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <Move className="mr-2 h-4 w-4" />
                Move to
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="max-h-60 overflow-y-auto">
              {stages.map((stage) => (
                <DropdownMenuItem key={stage.id} onClick={() => onBulkMove(stage.id)}>
                  {stage.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Add Tag */}
          <DropdownMenu onOpenChange={open => { if (open) handleTagDropdownOpen(); }}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <TagIcon className="mr-2 h-4 w-4" />
                Tag
                <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="max-h-60 overflow-y-auto">
              {tags.map((tag) => (
                <DropdownMenuItem key={tag.id} onClick={() => onBulkTag(tag.id)}>
                  {tag.tagName || tag.name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          <DropdownMenuSeparator />

          {/* Delete */}
          <Button variant="outline" size="sm" onClick={onBulkDelete}>
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </Button>
        </div>
      </div>

      <Button variant="ghost" size="sm" onClick={onClearSelection}>
        Clear selection
      </Button>
    </div>
  )
}
