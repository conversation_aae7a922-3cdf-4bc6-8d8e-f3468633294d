"use client"

import type React from "react"

import { useState, useContext } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, Calendar, ExternalLink } from "lucide-react"
import { format } from "date-fns"
import { Priority, Ticket } from "../ticket"
import type { User } from "../ticket"
import { TicketContext } from "../TicketContext"

interface TicketCardProps {
  ticket: Ticket
  isSelected: boolean
  onSelect: (ticketId: string, selected: boolean) => void
  onClick: (ticket: Ticket) => void
  onOpenInNewTab: (ticketId: string) => void
  isDragging?: boolean
}

const priorityColors: Record<Priority, string> = {
  low: "bg-gray-100 text-gray-800",
  medium: "bg-blue-100 text-blue-800",
  high: "bg-orange-100 text-orange-800",
  urgent: "bg-red-100 text-red-800",
}

const priorityIcons: Record<Priority, string> = {
  low: "↓",
  medium: "→",
  high: "↑",
  urgent: "⚠",
}

export function TicketCard({
  ticket,
  isSelected,
  onSelect,
  onClick,
  onOpenInNewTab,
  isDragging = false,
}: TicketCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { users } = useContext(TicketContext);

  const handleCardClick = (e: React.MouseEvent) => {
    if (
      e.target instanceof HTMLElement &&
      (e.target.closest("[data-dropdown-trigger]") || e.target.closest("[data-checkbox]"))
    ) {
      return
    }
    onClick(ticket)
  }

  const handleCheckboxChange = (checked: boolean) => {
    onSelect(ticket.id, checked)
  }

  // Get current stage
  const currentStage = ticket.currentStage;
  // Use assignedUser from currentStage
  const assignedUser = currentStage?.assignedUser;
  let assignedToDisplay;
  if (assignedUser) {
    assignedToDisplay = assignedUser.username || assignedUser.id;
  } else {
    assignedToDisplay = currentStage?.assignedTo || "Unassigned";
  }

  return (
    <Card
      className={`
        w-full
        cursor-pointer transition-all duration-200 hover:shadow-md
        ${isSelected ? "ring-2 ring-blue-500 bg-blue-50" : ""}
        ${isDragging ? "opacity-50 rotate-2 shadow-lg" : ""}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        {/* Header with checkbox and menu */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Checkbox
              data-checkbox
              checked={isSelected}
              onCheckedChange={handleCheckboxChange}
              onClick={e => e.stopPropagation()}
              className={`transition-opacity ${isHovered || isSelected ? "opacity-100" : "opacity-0"}`}
              aria-label={`Select ticket ${ticket.title}`}
            />
            <Badge className={priorityColors[ticket.priority]} variant="secondary">
              <span className="mr-1">{priorityIcons[ticket.priority]}</span>
              {ticket.priority}
            </Badge>
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild data-dropdown-trigger data-menu>
              <Button
                variant="ghost"
                size="sm"
                className={`h-8 w-8 p-0 transition-opacity ${isHovered ? "opacity-100" : "opacity-0"}`}
                aria-label="Ticket actions"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" data-menu>
              <DropdownMenuItem onClick={() => onClick(ticket)}>View Details</DropdownMenuItem>
              <DropdownMenuItem onClick={() => onOpenInNewTab(ticket.id)}>
                <ExternalLink className="mr-2 h-4 w-4" />
                Open in New Tab
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Title */}
        <h3 className="font-medium text-sm mb-1 line-clamp-2 text-gray-900 flex items-center gap-2">
          {ticket.title}
          <span className="ml-2 px-2 py-0.5 rounded-full bg-yellow-100 text-yellow-800 text-xs font-semibold">
            {ticket.comments?.length ?? 0} 💬
          </span>
        </h3>

        {/* Description */}
        {ticket.description && (
          <p className="text-xs text-gray-700 mb-2 line-clamp-2">{ticket.description}</p>
        )}

        {/* Tags */}
        {(ticket.tags?.length ?? 0) > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {(ticket.tags ?? []).map((tag) => (
              <Badge key={tag.id} className={`${tag.color} text-xs`}>
                {tag.tagName || tag.name}
              </Badge>
            ))}
          </div>
        )}

        {/* Footer */}
        <div className="flex items-center justify-between text-xs text-gray-500 mt-4">
          {/* Assigned To (bottom left) */}
          <div className="flex items-center space-x-2">
            {/* <span className="font-medium">Assigned To:</span> */}
            {assignedUser ? (
              <>
                <Avatar className="h-5 w-5">
                  <AvatarImage src={assignedUser.avatar || " "} alt={assignedToDisplay} />
                  <AvatarFallback className="text-xs">
                    {assignedUser.username ? assignedUser.username[0].toUpperCase() : ""}
                  </AvatarFallback>
                </Avatar>
                <span>{assignedToDisplay}</span>
              </>
            ) : (
              <span>{assignedToDisplay}</span>
            )}
          </div>
          {/* Due Date (bottom right) */}
          <div className="flex items-center space-x-1">
            <Calendar className="h-3 w-3" />
            <span className={currentStage?.dueAt && new Date(currentStage.dueAt) < new Date() ? "text-red-600" : ""}>
              {currentStage?.dueAt ? format(new Date(currentStage.dueAt), "MMM d") : "No due date"}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
