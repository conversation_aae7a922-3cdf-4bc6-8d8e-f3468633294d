"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import {
  DndContext,
  type DragEndEvent,
  DragOverlay,
  type DragStartEvent,
  PointerSensor,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import { TicketFilters as TicketFiltersComponent } from "./components/ticket-filters";
import { Button } from "@/components/ui/button";
import { RefreshCw, Layout, Sidebar, Minus } from "lucide-react";
import {
  Column,
  Ticket,
  TicketFilters,
  TicketStatus,
  User,
  Tag,
  Pipeline,
} from "./ticket";
import {
  fetchTickets,
  bulkDeleteTickets,
} from "./tickets";
import { BulkActions } from "./components/bulk-action";
import { KanbanColumn } from "./components/kanban-column";
import { TicketCard } from "./components/ticket-card";
import { TicketModal } from "./components/ticket-modal";
import { TicketSidebar } from "./components/ticket-sidebar";
import { getAllData } from "@/lib/helpers";
import { employee_routes, ticket_routes } from "@/lib/routePath";
import BreadCrumbs from "@/app/_component/BreadCrumbs";
import { TicketProvider, TicketContext } from "./TicketContext";

function TicketsPage() {
  const router = useRouter();
  // Use context for tickets, currentUser
  const { tickets, setTickets, currentUser, setCurrentUser } = React.useContext(TicketContext);
  const [filteredTickets, setFilteredTickets] = useState<Ticket[]>([]);
  const [selectedTickets, setSelectedTickets] = useState<string[]>([]);
  const [activeTicket, setActiveTicket] = useState<Ticket | null>(null);
  const [viewMode, setViewMode] = useState<"modal" | "sidebar">("modal");
  const [isLoading, setIsLoading] = useState(true);
  const [draggedTicket, setDraggedTicket] = useState<Ticket | null>(null);

  // Ref to prevent double API calls in React Strict Mode
  const hasLoadedTickets = useRef(false);

  const [filters, setFilters] = useState<TicketFilters>({
    search: "",
    stageIds: [],
    assignedTo: [],
    priority: [],
    tags: [],
    dateRange: {},
  });

  // DnD sensors: drag only starts after moving 15px
  const pointerSensor = useSensor(PointerSensor, {
    activationConstraint: {
      distance: 15,
    },
  });
  const sensors = useSensors(pointerSensor);

  // Define a Notion-style color palette for columns
  const columnColors = [
    { bg: "bg-red-50", badge: "bg-red-200", badgeText: "text-red-800" },
    { bg: "bg-pink-50", badge: "bg-pink-200", badgeText: "text-pink-800" },
    {
      bg: "bg-purple-50",
      badge: "bg-purple-200",
      badgeText: "text-purple-800",
    },
    {
      bg: "bg-yellow-50",
      badge: "bg-yellow-200",
      badgeText: "text-yellow-800",
    },
    {
      bg: "bg-orange-50",
      badge: "bg-orange-200",
      badgeText: "text-orange-800",
    },
    { bg: "bg-green-50", badge: "bg-green-200", badgeText: "text-green-800" },
    { bg: "bg-blue-50", badge: "bg-blue-200", badgeText: "text-blue-800" },
    { bg: "bg-teal-50", badge: "bg-teal-200", badgeText: "text-teal-800" },
  ];

  // Fetch tickets
  const loadTickets = useCallback(async () => {
    setIsLoading(true);
    try {
      const data = await fetchTickets();
      setTickets(data);
      // Optionally extract tags from tickets if needed
      // setTags(...)
    } catch (error) {
      console.error("Failed to fetch tickets:", error);
    } finally {
      setIsLoading(false);
    }
  }, []); // Remove setTickets from dependencies - React state setters are stable

  // Initial load and polling
  useEffect(() => {
    // Prevent double API calls in React Strict Mode
    if (hasLoadedTickets.current) return;
    hasLoadedTickets.current = true;

    loadTickets();
    return () => {};
  }, [loadTickets]);

  useEffect(() => {
    let filtered = tickets;

    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(
        (ticket) =>
          ticket.title.toLowerCase().includes(searchLower) ||
          ticket.description.toLowerCase().includes(searchLower) ||
          (ticket.currentStage?.assignedTo?.toLowerCase?.() || "").includes(
            searchLower
          ) ||
          ticket.tags.some((tag) =>
            (tag.name || tag.tagName || "").toLowerCase().includes(searchLower)
          )
      );
    }

    // AssignedTo filter (per stage)
    if (filters.assignedTo.length > 0) {
      filtered = filtered.filter(
        (ticket) =>
          ticket.currentStage &&
          filters.assignedTo.includes(String(ticket.currentStage.assignedTo))
      );
    }

    // Priority filter
    if (filters.priority.length > 0) {
      filtered = filtered.filter((ticket) =>
        filters.priority.includes(ticket.priority)
      );
    }

    // Tags filter
    if (filters.tags.length > 0) {
      filtered = filtered.filter((ticket) =>
        ticket.tags.some((tag) => filters.tags.includes(tag.id))
      );
    }

    // Date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      filtered = filtered.filter((ticket) => {
        if (!ticket.dueDate) return false;
        const dueDate = new Date(ticket.dueDate);
        if (filters.dateRange.from && dueDate < filters.dateRange.from)
          return false;
        if (filters.dateRange.to && dueDate > filters.dateRange.to)
          return false;
        return true;
      });
    }
    if (filters.stageIds.length > 0) {
      filtered = filtered.filter((ticket) => {
        const stageId =
          ticket.currentStage?.pipelineStageId ||
          ticket.pipeline.stages?.[0]?.id;
        return filters.stageIds.includes(stageId);
      });
    }

    setFilteredTickets(filtered);
  }, [tickets, filters]);

  // Group tickets by pipeline
  const pipelinesMap: Record<
    string,
    { pipeline: Pipeline; tickets: Ticket[] }
  > = {};
  filteredTickets.forEach((ticket) => {
    const pipelineId = ticket.pipeline.id;
    if (!pipelinesMap[pipelineId]) {
      pipelinesMap[pipelineId] = { pipeline: ticket.pipeline, tickets: [] };
    }
    pipelinesMap[pipelineId].tickets.push(ticket);
  });
  const pipelines = Object.values(pipelinesMap);

  // Helper to get the current stage ID for a ticket
  const getCurrentStageId = (ticket: Ticket, pipeline: Pipeline) => {
    return ticket.currentStage?.pipelineStageId || pipeline.stages?.[0]?.id;
  };

  // Drag and drop handlers
  const handleDragStart = (event: DragStartEvent) => {
    const ticket = tickets.find((t) => t.id === event.active.id);
    setDraggedTicket(ticket || null);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedTicket(null);

    if (!over || active.id === over.id) return;

    const ticketId = active.id as string;
    const newStageId = over.id as string;

    // Find the ticket
    const ticket = tickets.find((t) => t.id === ticketId);
    if (!ticket) {
      return;
    }

    // Find the ticket's stage object for the new stage
    const newCurrentStage = ticket.stages?.find(
      (stage) => stage.pipelineStageId === newStageId
    );
    if (!newCurrentStage) {
      return;
    }

    setTickets((prev) =>
      prev.map((t) =>
        t.id === ticketId ? { ...t, currentStage: newCurrentStage } : t
      )
    );

    // Persist to backend
    try {
      let userId = currentUser?.username;
      
      // If currentUser is not available, fetch it directly
      if (!userId) {
        try {
          const userResponse = await fetch(employee_routes.GETCURRENT_USER, {
            method: "GET",
            credentials: "include", // This will include cookies
            headers: {
              "Content-Type": "application/json",
            },
          });
          if (userResponse.ok) {
            const userData = await userResponse.json();
            userId = userData.username;
          } else {
            throw new Error("Failed to fetch current user");
          }
        } catch (userError) {
          console.error("Failed to fetch current user:", userError);
          throw new Error("No current user available");
        }
      }
      
      if (!userId) {
        throw new Error("No current user loaded");
      }
      
      const requestBody = {
        ticketId,
        ticketStageId: newStageId,
        createdBy: userId,
      };
      
      const response = await fetch(ticket_routes.UPDATE_TICKET(ticketId), {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(requestBody),
      });
      
      const responseData = await response.json();
      
    } catch (error) {
      console.error("Failed to persist stage change:", error);
      // Optionally: revert frontend state or show a notification
    }
  };

  // Selection handlers
  const handleSelectTicket = (ticketId: string, selected: boolean) => {
    setSelectedTickets((prev) =>
      selected ? [...prev, ticketId] : prev.filter((id) => id !== ticketId)
    );
  };

  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      setSelectedTickets(filteredTickets.map((t) => t.id));
    } else {
      setSelectedTickets([]);
    }
  };

  // Bulk actions
  const handleBulkMove = async (stageId: string) => {
    try {
      setTickets((prev) => {
        const updated = prev.map((ticket) => {
          if (selectedTickets.includes(ticket.id)) {
            const newCurrentStage = ticket.stages?.find(
              (stage) => stage.pipelineStageId === stageId
            );
            if (!newCurrentStage) return ticket;
            return { ...ticket, currentStage: newCurrentStage };
          }
          return ticket;
        });
        return updated;
      });
      // Prepare payload
      const payload = {
        tickets: selectedTickets.map((ticketId) => ({
          ticketId,
          ticketStageId: stageId,
          createdBy: currentUser?.username,
        })),
      };
      
      // Persist to backend
      const response = await fetch(ticket_routes.BULK_UPDATE_TICKETS, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });
      const responseData = await response.json().catch(() => ({}));
      
      // Removed refetching tickets from server
      setSelectedTickets([]);
    } catch (error) {
      console.error("Failed to bulk move tickets:", error);
    }
  };

  const handleBulkTag = async (tagId: string) => {
    const tag = tickets.find((t) => t.id === tagId);
    if (!tag) return;

    try {
      setTickets((prev) =>
        prev.map((ticket) =>
          selectedTickets.includes(ticket.id)
            ? {
                ...ticket,
                tags: [...ticket.tags.filter((t) => t.id !== tagId), tag],
              }
            : ticket
        )
      );
      setSelectedTickets([]);
    } catch (error) {
      console.error("Failed to bulk tag tickets:", error);
    }
  };

  const handleBulkDelete = async () => {
    if (
      !confirm(
        `Are you sure you want to delete ${selectedTickets.length} tickets?`
      )
    )
      return;
    try {
      await bulkDeleteTickets(selectedTickets, currentUser?.username);
      setTickets((prev) =>
        prev.filter((ticket) => !selectedTickets.includes(ticket.id))
      );
      setSelectedTickets([]);
    } catch (error) {
      console.error("Failed to bulk delete tickets:", error);
    }
  };

  // Ticket detail handlers
  const handleTicketClick = (ticket: Ticket) => {
    // Always use the latest ticket object from context
    const latestTicket = tickets.find(t => t.id === ticket.id);
    setActiveTicket(latestTicket || ticket);
  };

  const handleOpenInNewTab = (ticketId: string) => {
    window.open(`/pms/manage_tickets/${ticketId}`, "_blank");
  };

  const handleCloseDetail = () => {
    setActiveTicket(null);
  };

  // Add this function to refetch tickets after tag changes
  const handleTagsUpdated = async () => {
    // No need to refetch all tickets since tags are updated locally via onTagsChange callback
    // This prevents unnecessary API calls to /api/tickets
    console.log("Tags updated - using local state updates instead of refetching all tickets");
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-full mx-auto">
        {/* Breadcrumbs */}
        <BreadCrumbs
          breadcrumblist={[
            { link: "/pms", name: "Dashboard" },
            { link: "/pms/manage_tickets", name: "Tickets" },
          ]}
        />
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tickets</h1>
            <p className="text-gray-600 mt-1">
              Manage and track your tickets here
            </p>
          </div>

          <div className="flex items-center space-x-2 mt-4 sm:mt-0">
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                setViewMode(viewMode === "modal" ? "sidebar" : "modal")
              }
            >
              {viewMode === "modal" ? (
                <Sidebar className="mr-2 h-4 w-4" />
              ) : (
                <Layout className="mr-2 h-4 w-4" />
              )}
              {viewMode === "modal" ? "Sidebar View" : "Modal View"}
            </Button>

            <Button variant="outline" size="sm" onClick={loadTickets}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6">
          <TicketFiltersComponent
            filters={filters}
            onFiltersChange={setFilters}
            stages={Array.from(
              new Map(
                tickets
                  .flatMap((t) => t.pipeline.stages)
                  .map((stage) => [stage.id, stage])
              ).values()
            )}
            users={[]}
          />
        </div>

        {/* Bulk Actions */}
        <BulkActions
          selectedCount={selectedTickets.length}
          onBulkMove={handleBulkMove}
          onBulkTag={handleBulkTag}
          onBulkDelete={handleBulkDelete}
          onClearSelection={() => setSelectedTickets([])}
          stages={(() => {
            const firstSelected = tickets.find(
              (t) => t.id === selectedTickets[0]
            );
            return firstSelected?.pipeline?.stages || [];
          })()}
          users={[]}
        />

        {/* Render all pipelines */}
        {pipelines.map(({ pipeline, tickets }, pipelineIdx) => {
          // Group tickets by stage, handle missing stages
          const columns = Array.isArray(pipeline.stages)
            ? pipeline.stages.map((stage) => ({
                ...stage,
                tickets: tickets.filter(
                  (ticket) => getCurrentStageId(ticket, pipeline) === stage.id
                ),
              }))
            : [];
          return (
            <div key={pipeline.id} className="mb-12">
              <h2 className="flex items-center gap-2 text-2xl font-semibold mb-4">
                <span
                  className={`w-2 h-8 rounded-full ${columnColors[0].badge}`}
                ></span>
                {pipeline.name}
              </h2>
              <DndContext
                sensors={sensors}
                onDragStart={handleDragStart}
                onDragEnd={handleDragEnd}
              >
                <div className="flex flex-wrap gap-x-6 overflow-x-auto pb-6">
                  {columns.map((column, idx) => (
                    <KanbanColumn
                      key={column.id}
                      id={column.id}
                      title={column.name}
                      bgColor={columnColors[idx % columnColors.length].bg}
                      badgeColor={columnColors[idx % columnColors.length].badge}
                      badgeTextColor={
                        columnColors[idx % columnColors.length].badgeText
                      }
                      tickets={column.tickets}
                      selectedTickets={selectedTickets}
                      onSelectTicket={handleSelectTicket}
                      onTicketClick={handleTicketClick}
                      onOpenInNewTab={handleOpenInNewTab}
                    />
                  ))}
                </div>
                <DragOverlay>
                  {draggedTicket && (
                    <TicketCard
                      ticket={draggedTicket}
                      isSelected={false}
                      onSelect={() => {}}
                      onClick={() => {}}
                      onOpenInNewTab={() => {}}
                      isDragging
                    />
                  )}
                </DragOverlay>
              </DndContext>
            </div>
          );
        })}

        {/* Ticket Details */}
        {viewMode === "modal" ? (
          <TicketModal
            ticket={activeTicket}
            isOpen={!!activeTicket && viewMode === "modal"}
            onClose={handleCloseDetail}
            onOpenInNewTab={handleOpenInNewTab}
            onTagsUpdated={handleTagsUpdated}
          />
        ) : (
          <TicketSidebar
            ticket={activeTicket}
            isOpen={!!activeTicket && viewMode === "sidebar"}
            onClose={handleCloseDetail}
            onOpenInNewTab={handleOpenInNewTab}
            onTagsUpdated={handleTagsUpdated}
          />
        )}
      </div>
    </div>
  );
}

export default TicketsPage;
