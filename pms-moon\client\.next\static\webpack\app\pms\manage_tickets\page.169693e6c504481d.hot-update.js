"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/manage_tickets/page",{

/***/ "(app-pages-browser)/./app/pms/manage_tickets/tickets.ts":
/*!*******************************************!*\
  !*** ./app/pms/manage_tickets/tickets.ts ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bulkDeleteTickets: function() { return /* binding */ bulkDeleteTickets; },\n/* harmony export */   deleteTicket: function() { return /* binding */ deleteTicket; },\n/* harmony export */   fetchTags: function() { return /* binding */ fetchTags; },\n/* harmony export */   fetchTicket: function() { return /* binding */ fetchTicket; },\n/* harmony export */   fetchTickets: function() { return /* binding */ fetchTickets; },\n/* harmony export */   fetchUsers: function() { return /* binding */ fetchUsers; },\n/* harmony export */   updateTicket: function() { return /* binding */ updateTicket; },\n/* harmony export */   updateTicketStatus: function() { return /* binding */ updateTicketStatus; }\n/* harmony export */ });\n/* harmony import */ var _lib_routePath__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/routePath */ \"(app-pages-browser)/./lib/routePath.ts\");\n\n// Fetch all tickets\nasync function fetchTickets() {\n    // Add query parameter to make it more identifiable in Network tab\n    const ticketsUrl = \"\".concat(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.GET_TICKETS, \"?context=tickets-list\");\n    const res = await fetch(ticketsUrl, {\n        method: \"GET\"\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch tickets\");\n    const data = await res.json();\n    // Map API response to UI Ticket interface\n    return data.data.map((apiTicket)=>{\n        // Find the correct current stage object\n        let currentStage = undefined;\n        if (apiTicket.currentStageId && Array.isArray(apiTicket.stages)) {\n            currentStage = apiTicket.stages.find((stage)=>stage.pipelineStageId === apiTicket.currentStageId);\n        } else if (apiTicket.currentStage) {\n            currentStage = apiTicket.currentStage;\n        } else if (Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0) {\n            currentStage = apiTicket.stages[apiTicket.stages.length - 1];\n        }\n        return {\n            id: apiTicket.id,\n            title: apiTicket.title || \"Ticket for Invoice \".concat(apiTicket.workItemId),\n            description: apiTicket.description || \"\",\n            status: \"todo\",\n            priority: (apiTicket.priority || \"low\").toLowerCase(),\n            dueDate: Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0 ? apiTicket.stages[apiTicket.stages.length - 1].dueAt : null,\n            createdAt: apiTicket.createdAt ? new Date(apiTicket.createdAt) : new Date(),\n            updatedAt: apiTicket.updatedAt ? new Date(apiTicket.updatedAt) : new Date(),\n            tags: apiTicket.tags || [],\n            comments: apiTicket.comments || [],\n            pipeline: apiTicket.pipeline || {},\n            currentStage: currentStage,\n            stages: apiTicket.stages || [],\n            createdBy: apiTicket.createdBy || \"\",\n            owner: apiTicket.owner || \"\"\n        };\n    });\n}\n// Fetch all users\nasync function fetchUsers() {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.employee_routes.GETALL_USERS, {\n        method: \"GET\",\n        credentials: \"include\"\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch users\");\n    const data = await res.json();\n    // Map id to string\n    return data.data.map((user)=>({\n            ...user,\n            id: String(user.id)\n        }));\n}\n// Fetch a single ticket by ID\nasync function fetchTicket(id) {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.GET_TICKET_BY_ID(id), {\n        method: \"GET\"\n    });\n    if (!res.ok) return null;\n    const data = await res.json();\n    const apiTicket = data.data;\n    // Map tags to UI format\n    const tags = (apiTicket.tags || []).map((tag)=>({\n            id: tag.id,\n            tagName: tag.tagName || tag.name,\n            name: tag.tagName || tag.name,\n            color: tag.color || \"bg-gray-100 text-gray-800\",\n            createdBy: tag.createdBy,\n            createdAt: tag.createdAt\n        }));\n    // Map dueDate from last stage if available\n    let dueDate = null;\n    if (Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0) {\n        dueDate = apiTicket.stages[apiTicket.stages.length - 1].dueAt || null;\n    }\n    return {\n        id: apiTicket.id,\n        title: apiTicket.title || \"Ticket for Invoice \".concat(apiTicket.workItemId),\n        description: apiTicket.description || \"\",\n        status: apiTicket.status || \"todo\",\n        priority: (apiTicket.priority || \"low\").toLowerCase(),\n        dueDate: dueDate ? new Date(dueDate) : null,\n        createdAt: apiTicket.createdAt ? new Date(apiTicket.createdAt) : new Date(),\n        updatedAt: apiTicket.updatedAt ? new Date(apiTicket.updatedAt) : new Date(),\n        tags,\n        comments: apiTicket.comments || [],\n        pipeline: apiTicket.pipeline || {},\n        currentStage: apiTicket.currentStage || undefined,\n        currentStageId: apiTicket.currentStageId,\n        stages: apiTicket.stages || [],\n        createdBy: apiTicket.createdBy || \"\",\n        owner: apiTicket.owner || \"\"\n    };\n}\n// Update a ticket (status or other fields)\nasync function updateTicket(id, updates, username) {\n    const payload = {\n        ...updates,\n        ticketId: id,\n        ...username && {\n            updatedBy: username\n        }\n    };\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.UPDATE_TICKET(id), {\n        method: \"PUT\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!res.ok) throw new Error(\"Failed to update ticket\");\n    const data = await res.json();\n    return data.ticket || data.data;\n}\n// Update only the status of a ticket\nasync function updateTicketStatus(id, status, username) {\n    // The backend expects the full update body, so send only status if that's all that's changing\n    return updateTicket(id, {\n        status\n    }, username);\n}\n// Delete a ticket by ID\nasync function deleteTicket(id, username) {\n    const payload = username ? {\n        deletedBy: username\n    } : {};\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.ticket_routes.DELETE_TICKET(id), {\n        method: \"DELETE\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify(payload)\n    });\n    if (!res.ok) throw new Error(\"Failed to delete ticket\");\n}\n// Bulk delete tickets by looping over IDs\nasync function bulkDeleteTickets(ids, username) {\n    for (const id of ids){\n        await deleteTicket(id, username);\n    }\n}\n// Fetch all tags\nasync function fetchTags() {\n    const res = await fetch(_lib_routePath__WEBPACK_IMPORTED_MODULE_0__.tag_routes.GET_ALL_TAGS, {\n        method: \"GET\"\n    });\n    if (!res.ok) throw new Error(\"Failed to fetch tags\");\n    const data = await res.json();\n    return data.data;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/tickets.ts\n"));

/***/ })

});