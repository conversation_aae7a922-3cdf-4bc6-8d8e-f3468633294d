export interface User {
  username: string
  id: string
  name: string
  email: string
  avatar?: string
}

export interface Tag {
  id: string
  tagName: string
  name?: string
  color: string
  createdBy?: string
  createdAt?: string
  updatedBy?: string
  updatedAt?: string
}

export interface Stage {
  assignedUser: any
  color: string
  id: string;
  name: string;
  order: number;
  assignedTo?: string;
  dueAt?: string | Date;
  pipelineStageId?: string;
}

export interface Pipeline {
  id: string;
  name: string;
  stages: Stage[];
}

export interface Comment {
  id: string
  content: string
  createdBy: string
  createdAt: string
  updatedAt?: string
}

export interface Ticket {
  owner: string
  createdBy: string
  id: string
  title: string
  description: string
  status: string
  priority: string
  dueDate?: Date
  createdAt: Date
  updatedAt: Date
  tags: Tag[]
  comments: Comment[]
  pipeline: Pipeline
  currentStage?: Stage
  currentStageId?: string
  stages?: any[]
}

export type TicketStatus = "todo" | "in-progress" | "review" | "done"
export type Priority = "low" | "medium" | "high" 

export interface Column {
  id: TicketStatus
  title: string
  color: string
  tickets: Ticket[]
}

export interface TicketFilters {
  search: string
  stageIds: string[]
  assignedTo: string[]
  priority: string[]
  tags: string[]
  dateRange: {
    from?: Date
    to?: Date
  }
}

export interface BulkAction {
  type: "move" | "assign" | "tag" | "delete"
  payload: any
}
