model ClientCustomFieldArrangement {
  id              String @id @default(uuid())
  client_id       Int
  custom_field_id String
  order           Int

  Client      Client      @relation(fields: [client_id], references: [id], onDelete: Cascade)
  CustomField CustomField @relation(fields: [custom_field_id], references: [id], onDelete: Cascade)

  @@map("client_custom_field_arrangements")
}

model Comment {
  id      String @id @default(uuid())
  content String @db.Text()

  ticketId String @map("ticket_id")
  ticket   Ticket @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  createdBy String?   @map("created_by") @db.VarChar()
  createdAt DateTime  @default(now()) @map("created_at")
  updatedBy String?   @map("updated_by") @db.VarChar()
  updatedAt DateTime? @updatedAt @map("updated_at")
  deletedBy String?   @map("deleted_by") @db.VarChar()
  deletedAt DateTime? @map("deleted_at")

  @@map("comments")
}

model ManifestDetailsSchema {
  id           String      @id @default(uuid())
  trackSheetId Int         @unique
  trackSheet   TrackSheets @relation("TrackSheetToManifest", fields: [trackSheetId], references: [id], onDelete: Cascade)

  manifestStatus String?   @map("manifest_status") @db.VarChar()
  manifestDate   DateTime? @map("manifest_date")
  manifestNotes  String?   @map("manifest_notes") @db.VarChar()
  actionRequired String?   @map("action_required") @db.VarChar()

  createdAt DateTime  @default(now()) @map("created_at")
  createdBy String?   @map("created_by")
  updatedAt DateTime? @updatedAt @map("updated_at")
  updatedBy String?   @map("updated_by")
  deletedAt DateTime? @map("deleted_at")
  deletedBy String?   @map("deleted_by")

  @@index([trackSheetId])
}

model ManualMatchingMapping {
  id                String       @id @default(uuid())
  division          String?      @db.VarChar()
  company           String?      @db.VarChar()
  brandDivisionName String?      @db.VarChar()
  ManualShipment    String?      @db.VarChar()
  corporationId     Int?
  corporation       Corporation? @relation(fields: [corporationId], references: [corporation_id], onDelete: Cascade)
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @default(now()) @updatedAt

  @@map("manual_matching_mappings")
}

enum Worktype {
  trackSheets
}

model Pipeline {
  id          String   @id @default(uuid())
  name        String?  @db.VarChar()
  description String?  @db.VarChar()
  workType    Worktype @map("work_type")
  isActive    Boolean? @default(true) @map("is_active")

  stages PipelineStage[]

  // tickets   Ticket[]
  corporationId Int?         @map("corporation_id")
  corporation   Corporation? @relation(fields: [corporationId], references: [corporation_id], onDelete: Cascade)
  Ticket        Ticket[]

  createdAt DateTime  @default(now()) @map("created_at")
  createdBy String?   @map("created_by")
  updatedAt DateTime? @updatedAt @map("updated_at")
  updatedBy String?   @map("updated_by")
  deletedAt DateTime? @map("deleted_at")
  deletedBy String?   @map("deleted_by")

  @@map("pipelines")
}

model PipelineStage {
  id String @id @default(uuid())

  pipelineId String?   @map("pipeline_id")
  pipeline   Pipeline? @relation(fields: [pipelineId], references: [id], onDelete: Cascade)

  name        String? @db.VarChar()
  description String? @db.VarChar()
  order       Int
  // dueOffsetHours Int?    @map("due_offset_hours")

  createdAt   DateTime      @default(now()) @map("created_at")
  createdBy   String?       @map("created_by")
  updatedAt   DateTime?     @updatedAt @map("updated_at")
  updatedBy   String?       @map("updated_by")
  deletedAt   DateTime?     @map("deleted_at")
  deletedBy   String?       @map("deleted_by")
  TicketStage TicketStage[]

  // ticketStages   TicketStage[]
  @@unique([pipelineId, order])
  @@map("pipeline_stages")
}

model Tag {
  id      String @id @default(uuid())
  tagName String @unique @map("tag_name") @db.VarChar()
  color   String @db.VarChar()

  createdBy String?   @map("created_by") @db.VarChar()
  createdAt DateTime  @default(now()) @map("created_at")
  updatedBy String?   @map("updated_by") @db.VarChar()
  updatedAt DateTime? @updatedAt @map("updated_at")
  deletedBy String?   @map("deleted_by") @db.VarChar()
  deletedAt DateTime? @map("deleted_at")

  @@map("tags")
}

model Ticket {
  id          String  @id @default(uuid())
  title       String? @db.VarChar()
  description String? @db.Text()
  owner       String? @db.VarChar()
  priority    String? @db.VarChar()
  workItemId  Int     @map("work_item_id") // this should be unique

  pipelineId String?   @map("pipeline_id")
  pipeline   Pipeline? @relation(fields: [pipelineId], references: [id], onDelete: Cascade)

  stages   TicketStage[]
  comments Comment[]
  tags     String[]      @db.VarChar()

  currentStageId String? @map("current_stage_id")

  createdAt DateTime  @default(now()) @map("created_at")
  createdBy String?   @map("created_by")
  updatedAt DateTime? @updatedAt @map("updated_at")
  updatedBy String?   @map("updated_by")
  deletedAt DateTime? @map("deleted_at")
  deletedBy String?   @map("deleted_by")

  @@map("tickets")
}

model TicketStage {
  id String @id @default(uuid())

  ticketId String? @map("ticket_id")
  ticket   Ticket? @relation(fields: [ticketId], references: [id], onDelete: Cascade)

  pipelineStageId String?        @map("pipeline_stage_id")
  pipelineStage   PipelineStage? @relation(fields: [pipelineStageId], references: [id], onDelete: Cascade)

  assignedTo String?   @map("assigned_to") @db.VarChar()
  dueAt      DateTime? @map("due_at")

  createdAt DateTime  @default(now()) @map("created_at")
  createdBy String?   @map("created_by")
  updatedAt DateTime? @updatedAt @map("updated_at")
  updatedBy String?   @map("updated_by")
  deletedAt DateTime? @map("deleted_at")
  deletedBy String?   @map("deleted_by")

  @@index([assignedTo, dueAt])
  @@map("ticket_stages")
}

model TrackSheetCustomFieldMapping {
  id String @id @default(uuid())

  tracksheetId Int?
  trackSheet   TrackSheets? @relation(fields: [tracksheetId], references: [id], onDelete: Cascade)

  customFieldId String?
  customField   CustomField? @relation(fields: [customFieldId], references: [id], onDelete: Cascade)
  value         String?
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @default(now()) @updatedAt

  @@map("track_sheet_custom_field_mappings")
}

enum ImportStatus {
  inProgress @map("in_progress")
  success
  failure
}

model TrackSheetImport {
  id       String       @id @default(uuid())
  fileName String?      @map("file_name") @db.VarChar()
  status   ImportStatus
  location String?      @db.VarChar()

  TrackSheetImportError TrackSheetImportError[]
  TrackSheets           TrackSheets[]

  createdAt DateTime  @default(now()) @map("created_at")
  createdBy String?   @map("created_by")
  updatedAt DateTime? @updatedAt @map("updated_at")
  updatedBy String?   @map("updated_by")
  deletedAt DateTime? @map("deleted_at")
  deletedBy String?   @map("deleted_by")

  @@map("track_sheet_imports")
}

model TrackSheetImportError {
  id String @id @default(uuid())

  trackSheetImportId String?           @map("track_sheet_import_id")
  trackSheetImport   TrackSheetImport? @relation(fields: [trackSheetImportId], references: [id], onDelete: Cascade)

  rowNumber        Int?    @map("row_number")
  conflictingValue String? @map("conflicting_value") @db.VarChar()
  reason           String? @db.VarChar()

  createdAt DateTime  @default(now()) @map("created_at")
  createdBy String?   @map("created_by")
  updatedAt DateTime? @updatedAt @map("updated_at")
  updatedBy String?   @map("updated_by")
  deletedAt DateTime? @map("deleted_at")
  deletedBy String?   @map("deleted_by")

  @@map("track_sheet_import_errors")
}

model Associate {
  id             Int          @id @default(autoincrement())
  name           String       @db.VarChar()
  corporation_id Int?
  corporation    Corporation? @relation(fields: [corporation_id], references: [corporation_id])
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  Client         Client[]

  @@map("associates")
}

model Branch {
  id             Int          @id @default(autoincrement())
  branch_name    String       @db.VarChar()
  corporation_id Int?
  corporation    Corporation? @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade)
  Client         Client[]
  user           User[]
}

model Carrier {
  id                   Int                    @id @default(autoincrement())
  name                 String                 @unique @db.VarChar()
  carrier_code         String?                @unique
  code                 String?                @db.VarChar()
  country              String?                @db.VarChar()
  carrier_2nd_name     String?                @db.VarChar()
  created_at           DateTime               @default(now())
  updated_at           DateTime               @default(now()) @updatedAt
  corporation_id       Int?
  corporation          Corporation?           @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade)
  WorkReport           WorkReport[]
  ClientCarrier        ClientCarrier[]
  DailyPlanningDetails DailyPlanningDetails[]

  TrackSheets TrackSheets[]
  createdBy   String?       @map("created_by") @db.VarChar()
}

model Category {
  id             Int          @id @default(autoincrement())
  category_name  String       @db.VarChar()
  corporation_id Int?
  corporation    Corporation? @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade)
  WorkType       WorkType[]
  WorkReport     WorkReport[]
}

model Client {
  id             Int             @id @default(autoincrement())
  corporation_id Int?
  client_name    String          @unique @db.VarChar()
  ownership_id   Int?
  ownership      User?           @relation("OwnershipClients", fields: [ownership_id], references: [id])
  owner_name     String?         @db.VarChar()
  country        String?         @db.VarChar()
  branch_id      Int?
  branch         Branch?         @relation(fields: [branch_id], references: [id], onDelete: Cascade)
  corporation    Corporation?    @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade)
  associateId    Int?
  associate      Associate?      @relation(fields: [associateId], references: [id], onDelete: Cascade)
  created_at     DateTime        @default(now()) @db.Timestamptz(6)
  updated_at     DateTime        @updatedAt @db.Timestamptz()
  WorkReport     WorkReport[]
  ClientCarrier  ClientCarrier[]
  DailyPlanning  DailyPlanning[]
  userClients    UserClients[]
  // customFields removed - now managed through ClientCustomFieldArrangement

  TrackSheets TrackSheets[]

  ClientCustomFieldArrangement ClientCustomFieldArrangement[]

  ClientFTPFilePathConfig ClientFTPFilePathConfig[]
}

model ClientCarrier {
  id             Int         @id @default(autoincrement())
  corporation_id Int
  corporation    Corporation @relation(fields: [corporation_id], references: [corporation_id])
  client_id      Int?
  client         Client?     @relation(fields: [client_id], references: [id], onDelete: Cascade)
  carrier_id     Int?
  carrier        Carrier?    @relation(fields: [carrier_id], references: [id], onDelete: Cascade)
  created_at     DateTime    @default(now())
  updated_at     DateTime    @default(now()) @updatedAt
  payment_terms  String?     @db.VarChar()
}

model ClientFTPFilePathConfig {
  clientId  Int      @id
  filePath  String
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  client Client @relation(fields: [clientId], references: [id])

  @@map("client_ftp_file_path_config")
}

model Corporation {
  corporation_id       Int                    @id @default(autoincrement())
  username             String                 @unique
  email                String                 @unique @db.VarChar()
  password             String                 @db.VarChar()
  country              String?                @db.VarChar()
  state                String?                @db.VarChar()
  city                 String?                @db.VarChar()
  address              String?                @db.VarChar()
  created_at           DateTime               @default(now()) @db.Timestamptz(6)
  updated_at           DateTime               @updatedAt @db.Timestamptz()
  User                 User[]
  Client               Client[]
  Carrier              Carrier[]
  Roles                Roles[]
  ClientCarrier        ClientCarrier[]
  DailyPlanning        DailyPlanning[]
  DailyPlanningDetails DailyPlanningDetails[]
  ImageFile            ImageFile[]
  Category             Category[]
  branch               Branch[]
  Associate            Associate[]

  LegrandMapping LegrandMapping[]

  ManualMatchingMapping ManualMatchingMapping[]

  Pipeline Pipeline[]
}

model CustomField {
  id   String    @id @default(uuid())
  type FieldType

  autoOption AutoOption?
  name       String      @unique

  createdAt DateTime @default(now())
  createdBy String?

  updatedAt DateTime @updatedAt
  updatedBy String?

  TrackSheetCustomFieldMapping TrackSheetCustomFieldMapping[]
  ClientCustomFieldArrangement ClientCustomFieldArrangement[]

  @@map("custom_fields")
}

model DailyPlanning {
  id                   Int                    @id @default(autoincrement()) @map("daily_planning_id")
  corporation_id       Int?
  daily_planning_date  DateTime               @default(now())
  client_id            Int?
  corporation          Corporation?           @relation(fields: [corporation_id], references: [corporation_id])
  client               Client?                @relation(fields: [client_id], references: [id], onDelete: Cascade)
  user_id              Int?
  user                 User?                  @relation(fields: [user_id], references: [id], onDelete: Cascade)
  created_at           DateTime               @default(now())
  updated_at           DateTime               @default(now()) @updatedAt
  DailyPlanningDetails DailyPlanningDetails[]
  DailyPlanningByType  DailyPlanningByType[]

  @@unique([client_id, daily_planning_date])
}

model DailyPlanningByType {
  id                   Int                    @id @default(autoincrement())
  daily_planning_id    Int
  daily_planning       DailyPlanning          @relation(fields: [daily_planning_id], references: [id], onDelete: Cascade)
  type                 DailyPlanningType
  created_at           DateTime               @default(now())
  updated_at           DateTime               @default(now()) @updatedAt
  DailyPlanningDetails DailyPlanningDetails[]
}

model DailyPlanningDetails {
  id                        Int                  @id @default(autoincrement())
  corporation_id            Int?
  daily_planning_type_id    Int?
  daily_planning_type       DailyPlanningByType? @relation(fields: [daily_planning_type_id], references: [id], onDelete: Cascade)
  bucket                    Bucket?
  priority_status           PriorityStatus?
  corporation               Corporation?         @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade)
  user_id                   Int?
  user                      User?                @relation(fields: [user_id], references: [id], onDelete: Cascade)
  daily_planning_id         Int?
  daily_planning            DailyPlanning?       @relation(fields: [daily_planning_id], references: [id], onDelete: Cascade)
  carrier_id                Int?
  carrier                   Carrier?             @relation(fields: [carrier_id], references: [id], onDelete: Cascade)
  invoice_entry_total       Int?
  two_ten_error_total       Int?
  alloted                   Int?
  pending                   Int?
  review_status             Int?
  pf_status                 Int?
  batch_error               Int?
  reason                    String?              @db.VarChar()
  hold                      Int?
  two_ten_error             Int?
  two_ten_m_f               Int?
  two_ten_success           Int?
  two_ten_hold              Int?
  two_ten_import_additional Int?
  two_ten_manual_match      Int?
  old                       Int?
  new                       Int?
  ute                       Int?
  shipping_type             String?
  division                  String?              @db.VarChar()
  receive_by                String?
  receive_date              DateTime?
  review_by                 String?
  review_date               DateTime?
  reconcile_by              String?
  reconcile_date            DateTime?
  send_by                   String?
  send_date                 DateTime?
  no_invoices               Int?
  amount_of_invoice         Decimal?
  source                    String?
  age                       Int[]
  correct                   Int?
  entry                     Int?
  currency                  String?
  notes                     String?              @db.VarChar()
  type                      DailyPlanningType?
  created_at                DateTime             @default(now())
  updated_at                DateTime             @default(now()) @updatedAt
}

enum UserType {
  HR
  TL
  CSA
  MEMBER
}

enum WorkStatus {
  STARTED
  PAUSED
  RESUMED
  FINISHED
}

enum TaskType {
  BACKLOG
  REGULAR
}

enum DailyPlanningType {
  INVOICE_ENTRY_STATUS
  PF_STATUS
  REVIEW_STATUS
  BATCH_ERROR_STATUS
  TWO_TEN_ERROR
  TWO_TEN_SUCCESS
  TWO_TEN_M_F
  TWO_TEN_HOLD
  TWO_TEN_IMPORT_ADDITIONAL
  TWO_TEN_MANUAL_MATCH
  HOLD_STATUS
  STATEMENT_TABLE
  CORRECT
  ENTRY
}

enum Bucket {
  ZERO_TO_SEVEN
  EIGHT_TO_FIFTEEN
  SIXTEEN_TO_THIRTY
  THIRTY_ONE_TO_SIXTY
  SIXTY_ONE_TO_NINETY
  NINETY_ONE_TO_HUNDRED_AND_TWENTY
  HUNDRED_AND_TWENTY_PLUS
}

enum PriorityStatus {
  HIGH
  MEDIUM
  LOW
}

enum FieldType {
  TEXT
  NUMBER
  DATE
  AUTO
}

enum AutoOption {
  DATE
  USERNAME
}

enum Switchtype {
  INT
  EXT
}

model ImageFile {
  id             Int          @id @default(autoincrement())
  size           String
  name           String
  path           String
  type           String
  created_at     DateTime     @default(now()) @db.Timestamptz()
  updated_at     DateTime     @updatedAt @db.Timestamptz()
  user_id        Int
  user           User         @relation(fields: [user_id], references: [id])
  corporation_id Int?
  corporation    Corporation? @relation(fields: [corporation_id], references: [corporation_id])
}

model LegrandMapping {
  id                     String       @id @default(uuid())
  businessUnit           String?      @db.VarChar()
  legalName              String?      @db.VarChar()
  customeCode            String?      @db.VarChar()
  shippingBillingName    String?      @db.VarChar()
  shippingBillingAddress String?      @db.VarChar()
  location               String?      @db.VarChar()
  zipPostal              String?
  aliasCity              String?      @db.VarChar()
  aliasShippingNames     String?      @db.VarChar()
  corporationId          Int?
  corporation            Corporation? @relation(fields: [corporationId], references: [corporation_id], onDelete: Cascade)
  createdAt              DateTime     @default(now())
  updatedAt              DateTime     @default(now()) @updatedAt

  @@map("legrand_mappings")
}

model Roles {
  id              Int              @id @default(autoincrement())
  name            String           @db.VarChar()
  corporation_id  Int
  client_id       Int?
  created_at      DateTime         @default(now()) @db.Timestamptz(6)
  updated_at      DateTime         @updatedAt @db.Timestamptz()
  corporation     Corporation      @relation(fields: [corporation_id], references: [corporation_id], onDelete: Cascade, onUpdate: NoAction)
  permissions     Permissions[]
  role_permission RolePermission[]
  User            User[]
}

model Permissions {
  id              Int              @id @default(autoincrement())
  module          String           @db.VarChar()
  action          String           @db.VarChar()
  created_at      DateTime         @default(now()) @db.Timestamptz(6)
  updated_at      DateTime         @updatedAt @db.Timestamptz()
  roles           Roles[]
  role_permission RolePermission[]
}

model RolePermission {
  id            Int         @id @default(autoincrement())
  role_id       Int
  permission_id Int
  role          Roles       @relation(fields: [role_id], references: [id], onDelete: Cascade)
  permission    Permissions @relation(fields: [permission_id], references: [id])
  created_at    DateTime    @default(now())
  updated_at    DateTime    @default(now()) @updatedAt
}

// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id            Int      @id @default(autoincrement())
  user_id       Int      @unique
  session_token String   @unique
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt
  user          User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
}

model SuperAdmin {
  id         Int      @id @default(autoincrement())
  username   String   @unique
  email      String   @unique @db.VarChar()
  password   String   @db.VarChar()
  created_at DateTime @default(now()) @db.Timestamptz(6)
  updated_at DateTime @updatedAt @db.Timestamptz()
}

model TicketStageChangeLog {
  id String @id @default(uuid())

  ticketId  String? @map("ticket_id")
  fromStage String? @map("from_stage")
  toStage   String? @map("to_stage")

  createdAt DateTime  @default(now()) @map("created_at")
  createdBy String?   @map("created_by")
  updatedAt DateTime? @updatedAt @map("updated_at")
  updatedBy String?   @map("updated_by")
  deletedAt DateTime? @map("deleted_at")
  deletedBy String?   @map("deleted_by")

  @@map("ticket_stage_change_logs")
}

model TrackSheets {
  id                 Int       @id @default(autoincrement())
  clientId           Int?      @map("client_id")
  client             Client?   @relation(fields: [clientId], references: [id], onDelete: Cascade)
  company            String?
  division           String?
  masterInvoice      String?   @map("master_invoice") @db.VarChar()
  invoice            String?   @db.VarChar()
  bol                String?   @db.VarChar()
  invoiceDate        DateTime? @map("invoice_date")
  receivedDate       DateTime? @map("received_date")
  shipmentDate       DateTime? @map("shipment_date")
  carrierId          Int?      @map("carrier_id")
  carrier            Carrier?  @relation(fields: [carrierId], references: [id], onDelete: Cascade)
  invoiceStatus      String?   @map("invoice_status")
  manualMatching     String?   @map("manual_matching")
  invoiceType        String?   @map("invoice_type")
  currency           String?
  qtyShipped         Int?      @map("qty_shipped")
  weightUnitName     String?   @map("weight_unit_name")
  quantityBilledText String?   @map("quantity_billed_text") @db.VarChar()
  freightClass       String?   @map("freight_class") @db.VarChar()
  invoiceTotal       Decimal?  @map("invoice_total") @db.Decimal(20, 6)
  savings            String?   @db.VarChar()
  financialNotes     String?   @map("financial_notes") @db.VarChar()
  ftpFileName        String?   @map("ftp_file_name") @db.VarChar()
  ftpPage            String?   @map("ftp_page") @db.VarChar()
  filePath           String?   @map("file_path") @db.VarChar()
  billToClient       Boolean?  @default(false) @map("bill_to_client")
  docAvailable       String?   @map("doc_available")
  notes              String?   @db.VarChar()
  mistake            String?   @db.VarChar()

  manifestDetails ManifestDetailsSchema? @relation("TrackSheetToManifest")

  trackSheetImportId           String?                        @map("track_sheet_import_id")
  trackSheetImport             TrackSheetImport?              @relation(fields: [trackSheetImportId], references: [id], onDelete: Cascade)
  TrackSheetCustomFieldMapping TrackSheetCustomFieldMapping[]

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")
  enteredBy String?  @map("entered_by") @db.VarChar()

  @@index([invoice])
  @@map("track_sheets")
}

model User {
  id                   Int                    @id @default(autoincrement())
  corporation_id       Int?
  role_id              Int?
  role                 Roles?                 @relation(fields: [role_id], references: [id], onDelete: Cascade)
  firstName            String?                @db.VarChar()
  lastName             String?                @db.VarChar()
  email                String                 @unique @db.VarChar()
  username             String                 @unique @db.VarChar()
  password             String                 @db.VarChar()
  level                Int?
  parent_id            Int?
  date_of_joining      DateTime?              @db.Timestamptz()
  branch_id            Int?
  branch               Branch?                @relation(fields: [branch_id], references: [id], onDelete: Cascade)
  created_at           DateTime               @default(now()) @db.Timestamptz()
  updated_at           DateTime               @updatedAt @db.Timestamptz()
  corporation          Corporation?           @relation(fields: [corporation_id], references: [corporation_id])
  WorkReport           WorkReport[]
  DailyPlanningDetails DailyPlanningDetails[]
  DailyPlanning        DailyPlanning[]
  image                ImageFile[]
  session              Session[]
  userClients          UserClients[]
  ownershipClients     Client[]               @relation("OwnershipClients")
}

model UserClients {
  userId   Int
  clientId Int
  user     User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  client   Client @relation(fields: [clientId], references: [id], onDelete: Cascade)

  @@id([userId, clientId])
}

model UserTitle {
  id         Int      @id @default(autoincrement())
  level      Int?
  title      String?
  created_at DateTime @default(now())
  updated_at DateTime @default(now()) @updatedAt

  @@map("user_titles")
}

model VisibilityRule {
  id          Int      @id @default(autoincrement())
  level       Int?
  table_name  String?
  self        Boolean? @default(true)
  ancestors   Int?     @default(0)
  descendants Int?     @default(0)
  siblings    Boolean? @default(false)

  @@map("visibility_rules")
}

model WorkReport {
  id                Int         @id @default(autoincrement())
  date              DateTime    @db.Timestamptz()
  user_id           Int
  user              User?       @relation(fields: [user_id], references: [id], onDelete: Cascade)
  client_id         Int?
  client            Client?     @relation(fields: [client_id], references: [id], onDelete: Cascade)
  carrier_id        Int?
  carrier           Carrier?    @relation(fields: [carrier_id], references: [id], onDelete: Cascade)
  work_type_id      Int?
  work_type         WorkType?   @relation(fields: [work_type_id], references: [id], onDelete: Cascade)
  work_status       WorkStatus?
  task_type         TaskType?
  planning_nummbers String?     @db.VarChar()
  expected_time     DateTime?
  actual_number     Int?
  start_time        DateTime    @db.Timestamptz()
  finish_time       DateTime?   @db.Timestamptz()
  time_spent        Decimal?    @db.Decimal(10, 2)
  pause             DateTime[]
  resume            DateTime[]
  category_id       Int?
  category          Category?   @relation(fields: [category_id], references: [id], onDelete: Cascade)
  notes             String?     @db.VarChar()
  switch_type       Switchtype?
  created_at        DateTime    @default(now())
  updated_at        DateTime    @default(now()) @updatedAt

  @@index([user_id, work_status])
}

model WorkType {
  id                              Int          @id @default(autoincrement())
  work_type                       String       @db.VarChar()
  created_at                      DateTime     @default(now())
  updated_at                      DateTime     @default(now()) @updatedAt
  is_work_carrier_specific        Boolean      @default(true)
  does_it_require_planning_number Boolean      @default(true)
  is_backlog_regular_required     Boolean      @default(true)
  WorkReport                      WorkReport[]
  category_id                     Int?
  category                        Category?    @relation(fields: [category_id], references: [id], onDelete: Cascade)
}
