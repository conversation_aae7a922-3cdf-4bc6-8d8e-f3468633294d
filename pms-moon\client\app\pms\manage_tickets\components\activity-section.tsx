"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { Activity, ArrowRight } from "lucide-react"
import { ticket_routes } from "@/lib/routePath"

interface StageChangeLog {
  id: string
  ticketId: string
  fromStage: string
  toStage: string
  fromStageName: string
  toStageName: string
  createdBy: string
  createdAt: string
  updatedAt?: string
  updatedBy?: string
}

interface ActivitySectionProps {
  ticketId: string
}

// Color palette for different stages
const STAGE_COLORS = [
  "bg-blue-100 text-blue-700 border-blue-200",
  "bg-green-100 text-green-700 border-green-200",
  "bg-purple-100 text-purple-700 border-purple-200",
  "bg-orange-100 text-orange-700 border-orange-200",
  "bg-pink-100 text-pink-700 border-pink-200",
  "bg-indigo-100 text-indigo-700 border-indigo-200",
  "bg-teal-100 text-teal-700 border-teal-200",
  "bg-yellow-100 text-yellow-700 border-yellow-200",
  "bg-red-100 text-red-700 border-red-200",
  "bg-emerald-100 text-emerald-700 border-emerald-200",
  "bg-cyan-100 text-cyan-700 border-cyan-200",
  "bg-amber-100 text-amber-700 border-amber-200",
  "bg-rose-100 text-rose-700 border-rose-200",
  "bg-violet-100 text-violet-700 border-violet-200",
  "bg-slate-100 text-slate-700 border-slate-200",
  "bg-lime-100 text-lime-700 border-lime-200",
]

// Function to get consistent color for a stage name
const getStageColor = (stageName: string): string => {
  // Create a simple hash from the stage name to get consistent colors
  let hash = 0
  for (let i = 0; i < stageName.length; i++) {
    const char = stageName.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  const index = Math.abs(hash) % STAGE_COLORS.length
  return STAGE_COLORS[index]
}

export function ActivitySection({ ticketId }: ActivitySectionProps) {
  const [logs, setLogs] = useState<StageChangeLog[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Ref to prevent double API calls in React Strict Mode
  const hasLoadedLogs = useRef<string | null>(null)

  useEffect(() => {
    const fetchLogs = async () => {
      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(ticket_routes.GET_TICKET_STAGE_LOGS(ticketId))

        if (!response.ok) {
          throw new Error("Failed to fetch activity logs")
        }
        const data = await response.json()

        setLogs(data.data || [])
      } catch (err) {
        console.error("Error fetching activity logs:", err)
        setError("Failed to load activity logs")
      } finally {
        setIsLoading(false)
      }
    }

    // Prevent double API calls in React Strict Mode
    if (ticketId && hasLoadedLogs.current !== ticketId) {
      hasLoadedLogs.current = ticketId
      fetchLogs()
    }
  }, [ticketId])

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardContent className="p-0">
          <div className="flex items-center gap-2 text-base font-semibold h-[48px] pl-4 border-b border-gray-100 dark:border-gray-800">
            <Activity className="h-5 w-5" />
            Activity
          </div>
          <div className="flex items-center justify-center h-32">
            <div className="text-gray-500">Loading activity...</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="w-full">
        <CardContent className="p-0">
          <div className="flex items-center gap-2 text-base font-semibold h-[48px] pl-4 border-b border-gray-100 dark:border-gray-800">
            <Activity className="h-5 w-5" />
            Activity
          </div>
          <div className="flex items-center justify-center h-32">
            <div className="text-red-500">{error}</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardContent className="p-0">
        <div className="flex items-center gap-2 text-base font-semibold h-[48px] pl-4 border-b border-gray-100 dark:border-gray-800">
          <Activity className="h-5 w-5" />
          Activity
        </div>
        <div className="max-h-72 overflow-y-auto space-y-4 p-4 pt-3">
          {logs.length === 0 ? (
            <div className="text-center text-gray-500 py-8">
              No activity yet. Stage changes will appear here.
            </div>
          ) : (
            logs.map((log) => (
              <div key={log.id} className="group flex flex-col gap-1 p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                {/* Stage change content */}
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-xs text-gray-600 dark:text-gray-400">
                    Stage changed from
                  </span>
                  <Badge variant="outline" className={`text-[10px] px-2 py-0.5 ${getStageColor(log.fromStageName)}`}>
                    {log.fromStageName}
                  </Badge>
                  <ArrowRight className="h-3 w-3 text-gray-400" />
                  <Badge variant="outline" className={`text-[10px] px-2 py-0.5 ${getStageColor(log.toStageName)}`}>
                    {log.toStageName}
                  </Badge>
                </div>
                
                {/* Username and date/time */}
                <div className="flex items-center gap-2 text-[10px] text-gray-500">
                  <Avatar className="h-4 w-4">
                    <AvatarFallback className="text-[9px]">
                      {log.createdBy
                        .split(" ")
                        .map((n) => n[0])
                        .join("")
                        .toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <span className="font-medium text-[10px] text-gray-700 dark:text-gray-300">
                    {log.createdBy}
                  </span>
                  <span className="ml-auto">
                    {format(new Date(log.createdAt), "MMM d, yyyy 'at' h:mm a")}
                  </span>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
} 