"use client";

import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { X, Plus, User } from "lucide-react";
import { getAllData, getCookie } from "@/lib/helpers";
import {
  ticket_routes,
  employee_routes,
  pipeline_routes,
} from "@/lib/routePath";
import { toast } from "sonner";
import { getStageColor } from "@/lib/stageColorUtils";
import { formSubmit } from "@/lib/helpers";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

const PRIORITY_OPTIONS = [
  { value: "High", label: "High" },
  { value: "Medium", label: "Medium" },
  { value: "Low", label: "Low" },
];

const CreateTicketModal = ({
  isOpen,
  onClose,
  onClearSelection,
  selectedRows = [],
}) => {
  const [ticketForms, setTicketForms] = useState([]);
  const [users, setUsers] = useState([]);
  const [pipelines, setPipelines] = useState([]);
  const [creator, setCreator] = useState(null);
  const pipelineSelectRef = useRef(null);
  const dataFetched = useRef(false);
  const [arePipelinesLoading, setArePipelinesLoading] = useState(false);
  const [areUsersLoading, setAreUsersLoading] = useState(false);
  const [isBulkFill, setIsBulkFill] = useState(true);

  useEffect(() => {
    if (!isOpen) {
      dataFetched.current = false;
      setUsers([]);
      setPipelines([]);
      setCreator(null);
      return;
    }

    if (dataFetched.current) {
      return;
    }
    dataFetched.current = true;

    const fetchInitialData = async () => {
      try {
        // Fetch only current user info initially
        const currentUserInfo = await getAllData(
          employee_routes.GETCURRENT_USER
        );
        setCreator(currentUserInfo || null);
      } catch (err) {
        dataFetched.current = false;
        toast.error("Failed to load user data.");
      }
    };
    fetchInitialData();
  }, [isOpen]);

  // Fetch and set CM as owner when creator is set
  useEffect(() => {
    const fetchCMAndSetOwner = async () => {
      if (!creator || !creator.id) return;
      try {
        const res = await fetch(employee_routes.GET_CM(creator.id));
        if (!res.ok) throw new Error("Failed to fetch CM");
        const data = await res.json();
        const cm = data.cm;
        const ownerName = cm?.name || cm?.username || "";
        if (!ownerName) throw new Error("No CM found for this user");
        setTicketForms((prev) =>
          prev.map((form) => ({ ...form, owner: ownerName }))
        );
      } catch (err) {
        toast.error(
          "Could not auto-fill owner (CM): " + (err?.message || "Unknown error")
        );
      }
    };
    fetchCMAndSetOwner();
    // Only run when creator changes
  }, [creator]);

  useEffect(() => {
    if (isOpen) {
      const initialForms =
        selectedRows && selectedRows.length > 0
          ? selectedRows.map((row) => ({
              tracksheetid: row.id || "",
              pipeline_id: "",
              owner: "",
              priority: "",
              stages: [],
              pipelineStages: [],
            }))
          : [
              {
                tracksheetid: "",
                pipeline_id: "",
                owner: "",
                priority: "",
                stages: [],
                pipelineStages: [],
              },
            ];
      setTicketForms(initialForms);

      requestAnimationFrame(() => {
        setTimeout(() => {
          pipelineSelectRef.current?.focus();
        }, 30);
      });
    }
  }, [isOpen, selectedRows]);

  const handleFetchPipelines = async () => {
    if (pipelines.length > 0 || arePipelinesLoading) return;

    setArePipelinesLoading(true);
    try {
      const pipelinesData = await getAllData(pipeline_routes.GET_PIPELINE);
      setPipelines(pipelinesData.data || []);
    } catch (err) {
      toast.error("Failed to load pipelines.");
    } finally {
      setArePipelinesLoading(false);
    }
  };

  const handleFetchUsers = async () => {
    if (users.length > 0 || areUsersLoading) return;
    setAreUsersLoading(true);
    try {
      const usersData = await getAllData(employee_routes.GETALL_USERS);
      setUsers(usersData.data || []);
    } catch (err) {
      toast.error("Failed to load users.");
    } finally {
      setAreUsersLoading(false);
    }
  };

  const handleClose = () => {
    (document.activeElement as HTMLElement)?.blur();
    onClose();
    onClearSelection();
  };

  const handleFieldChange = (index, field, value) => {
    setTicketForms((prev) => {
      const shouldApplyToAll = isBulkFill && prev.length > 1;
      return prev.map((t, i) => {
        if (shouldApplyToAll || i === index) {
          if (field === "pipeline_id") {
            const selectedPipeline = pipelines.find((p) => p.id === value);
            const stages = selectedPipeline?.stages || [];
            return {
              ...t,
              pipeline_id: value,
              pipelineStages: stages,
              stages: stages.map((s) => ({
                stageid: s.id,
                assignedto: "",
                due: "",
              })),
            };
          }
          return { ...t, [field]: value };
        }
        return t;
      });
    });
  };

  const handleStageChange = (index, stageid, field, value) => {
    setTicketForms((prev) => {
      const shouldApplyToAll = isBulkFill && prev.length > 1;
      return prev.map((t, i) => {
        if (shouldApplyToAll || i === index) {
          const updatedStages = t.stages.map((s) =>
            s.stageid === stageid ? { ...s, [field]: value } : s
          );
          return { ...t, stages: updatedStages };
        }
        return t;
      });
    });
  };

  const handleAddTicketForm = () => {
    setTicketForms((prev) => [
      ...prev,
      {
        tracksheetid: "",
        description: "",
        pipeline_id: "",
        owner: "",
        priority: "",
        stages: [],
        pipelineStages: [],
      },
    ]);
  };

  const handleRemoveTicketForm = (index) => {
    setTicketForms((prev) => prev.filter((_, i) => i !== index));
  };

  const handleFinalSubmit = async () => {
    const validTickets = ticketForms.filter(
      (t) => t.pipeline_id && t.owner && t.priority && t.stages.length
    );
    if (!validTickets.length) return toast.error("Fill all required fields.");

    // Prepare the tickets to send (exclude pipelineStages, include title)
    const ticketsWithCreator = validTickets.map((ticket, index) => ({
      tracksheetid: ticket.tracksheetid,
      title: `Ticket for Invoice ${selectedRows[index]?.invoice || ticket.tracksheetid || "N/A"}`,
      description: ticket.description || "",
      pipeline_id: ticket.pipeline_id,
      owner: ticket.owner,
      priority: ticket.priority,
      stages: ticket.stages.map((s) => ({
        stageid: s.stageid,
        assignedto: s.assignedto,
        due: s.due,
      })),
      createdBy: creator?.username,
    }));

    console.log("Sending tickets to backend:", ticketsWithCreator);

    try {
      const data = await formSubmit(
        ticket_routes.CREATE_TICKET,
        "POST",
        ticketsWithCreator
      );
      if (
        data &&
        data.message &&
        data.message.toLowerCase().includes("success")
      ) {
        toast.success("Tickets created successfully.");
        handleClose();
      } else {
        toast.error((data && data.message) || "Failed to create.");
      }
    } catch {
      toast.error("Network/server error.");
    }
  };

  if (!isOpen) return null; // Don't render modal if not open

  return (
    // Overlay with fixed position covering the entire screen
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm px-4">
      {/* Modal Container */}
      <div className="bg-white dark:bg-gray-900 rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] p-6 relative transition-transform scale-100 border border-gray-200 dark:border-gray-800 flex flex-col">
        {/* Header */}
        <div className="mb-4 text-center">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Create New Ticket
          </h2>
          <p className="text-gray-600 dark:text-gray-400 text-base max-w-2xl mx-auto">
            Fill in the details below to create a new ticket.
          </p>
        </div>

        {/* Scrollable content for tickets */}
        <div className="flex-grow overflow-y-auto px-2 mb-4">
          {ticketForms.length > 1 && (
            <div className="flex items-center justify-end mb-4 space-x-2 pr-2">
              <Label htmlFor="bulk-fill-switch">Apply to all tickets</Label>
              <Switch
                id="bulk-fill-switch"
                checked={isBulkFill}
                onCheckedChange={setIsBulkFill}
              />
            </div>
          )}
          {ticketForms.map((ticket, index) => (
            <div
              key={index}
              className="bg-white dark:bg-gray-800 rounded-xl shadow-md p-4 mb-4 relative border border-gray-200 dark:border-gray-700"
            >
              {/* Remove button */}
              {ticketForms.length > 1 && (
                <button
                  className="absolute top-2 right-2 text-gray-400 hover:text-red-500 transition"
                  onClick={() => handleRemoveTicketForm(index)}
                >
                  <X className="w-4 h-4" />
                </button>
              )}

              {/* Ticket Header */}
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-xl font-semibold text-gray-800 dark:text-gray-200 flex items-center space-x-2">
                  <span className="text-gray-800 dark:text-gray-200">
                    Ticket for Invoice {selectedRows[index]?.invoice || ticket.tracksheetid || "N/A"}
                  </span>
                  <Badge
                    className={`
                      ${
                        ticket.priority === "High"
                          ? "bg-red-100 text-red-800"
                          : ticket.priority === "Medium"
                          ? "bg-yellow-100 text-yellow-800"
                          : ticket.priority === "Low"
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                      }
                      px-3 py-1 rounded-full text-sm font-medium shadow
                    `}
                  >
                    {ticket.priority || "Priority"}
                  </Badge>
                </h3>
              </div>

              {/* Main form */}
              <div className="grid md:grid-cols-4 gap-4 mb-4">
                {/* Owner */}
                <div className="flex flex-col">
                  <label className="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Owner <span className="text-red-500">*</span>
                  </label>
                  <Input
                    value={ticket.owner}
                    readOnly
                    className="w-full h-10 bg-gray-100 dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-4 py-2 shadow-sm focus:outline-none text-gray-700 dark:text-gray-300"
                    placeholder="Owner will be auto-filled"
                  />
                </div>

                {/* Pipeline */}
                <div className="flex flex-col">
                  <label className="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Pipeline <span className="text-red-500">*</span>
                  </label>
                  <Select
                    value={ticket.pipeline_id}
                    onValueChange={(value) =>
                      handleFieldChange(index, "pipeline_id", value)
                    }
                    onOpenChange={(open) => {
                      if (open) handleFetchPipelines();
                    }}
                  >
                    <SelectTrigger
                      ref={index === 0 ? pipelineSelectRef : null}
                      className="w-full h-10 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 transition"
                    >
                      <SelectValue placeholder="Select Pipeline" />
                    </SelectTrigger>
                    <SelectContent className="bg-white dark:bg-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto mt-1">
                      {arePipelinesLoading ? (
                        <div className="flex justify-center items-center p-2 text-gray-500">
                          Loading...
                        </div>
                      ) : (
                        pipelines.map((p) => (
                          <SelectItem
                            key={p.id}
                            value={p.id}
                            className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer transition"
                          >
                            {p.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                </div>

                {/* Priority */}
                <div className="flex flex-col">
                  <label className="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Priority <span className="text-red-500">*</span>
                  </label>
                  <Select
                    value={ticket.priority}
                    onValueChange={(value) =>
                      handleFieldChange(index, "priority", value)
                    }
                  >
                    <SelectTrigger className="w-full h-10 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-4 py-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 transition">
                      <SelectValue placeholder="Select Priority" />
                    </SelectTrigger>
                    <SelectContent className="bg-white dark:bg-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto mt-1">
                      {PRIORITY_OPTIONS.map((opt) => (
                        <SelectItem
                          key={opt.value}
                          value={opt.value}
                          className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer transition"
                        >
                          <div className="flex items-center space-x-2">
                            <Badge
                              className={`
                                ${
                                  opt.value === "High"
                                    ? "bg-red-100 text-red-800"
                                    : opt.value === "Medium"
                                    ? "bg-yellow-100 text-yellow-800"
                                    : opt.value === "Low"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
                                }
                                px-2 py-0.5 rounded-full text-xs font-medium
                              `}
                            >
                              {opt.label}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Description (inline) */}
                <div className="flex flex-col">
                  <label className="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                    Description
                  </label>
                  <textarea
                    value={ticket.description}
                    onChange={(e) => handleFieldChange(index, "description", e.target.value)}
                    className="w-full h-10 min-h-[36px] max-h-16 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-3 py-1 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 transition resize-none text-sm"
                    placeholder="Enter ticket description"
                    rows={1}
                  />
                </div>
              </div>

              {/* Stages Horizontal Steps */}
              {ticket.pipelineStages?.length > 0 && (
                <div className="overflow-x-auto py-2">
                  <div className="flex items-stretch gap-4">
                    {ticket.pipelineStages.map((stage, sidx) => (
                      <div
                        key={stage.id}
                        className="flex flex-col items-center bg-gray-50 dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg p-2 min-w-[160px] shadow-sm relative text-[13px]"
                      >
                        {/* Stage Name */}
                        <Badge
                          className={`${getStageColor(
                            stage.id
                          )} px-2 text-xs mb-1`}
                        >
                          {stage.name}
                        </Badge>
                        {/* Assign To */}
                        <div className="w-full mb-1">
                          <label className="block text-[11px] text-gray-500 mb-0.5">
                            Assign To <span className="text-red-500">*</span>
                          </label>
                          <Select
                            value={ticket.stages[sidx]?.assignedto || ""}
                            onValueChange={(value) =>
                              handleStageChange(
                                index,
                                stage.id,
                                "assignedto",
                                value
                              )
                            }
                            onOpenChange={(open) => {
                              if (open) handleFetchUsers();
                            }}
                          >
                            <SelectTrigger className="w-full h-8 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]">
                              <SelectValue placeholder="Select User" />
                            </SelectTrigger>
                            <SelectContent className="bg-white dark:bg-gray-700 rounded-lg shadow-lg max-h-60 overflow-y-auto mt-1">
                              {areUsersLoading ? (
                                <div className="flex justify-center items-center p-2 text-gray-500">
                                  Loading...
                                </div>
                              ) : (
                                users.map((u) => (
                                  <SelectItem
                                    key={u.id}
                                    value={u.id.toString()}
                                    className="px-2 py-1 hover:bg-gray-100 dark:hover:bg-gray-800 rounded cursor-pointer transition text-[13px]"
                                  >
                                    {u.username}
                                  </SelectItem>
                                ))
                              )}
                            </SelectContent>
                          </Select>
                        </div>
                        {/* Due Date */}
                        <div className="w-full">
                          <label className="block text-[11px] text-gray-500 mb-0.5">
                            Due Date <span className="text-red-500">*</span>
                          </label>
                          <Input
                            type="date"
                            className="w-full h-8 border border-gray-300 dark:border-gray-700 rounded-lg px-2 py-1 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-400 transition text-[13px]"
                            value={
                              ticket.stages[sidx]?.due?.split("T")[0] || ""
                            }
                            onChange={(e) =>
                              handleStageChange(
                                index,
                                stage.id,
                                "due",
                                new Date(e.target.value).toISOString()
                              )
                            }
                          />
                        </div>
                        {/* Arrow Divider */}
                        {sidx < ticket.pipelineStages.length - 1 && (
                          <div className="absolute right-[-22px] top-1/2 -translate-y-1/2 z-10">
                            <svg
                              width="24"
                              height="24"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <path
                                d="M4 12h16m0 0l-4-4m4 4l-4 4"
                                stroke="#9ca3af"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Add Ticket Icon Button (right-aligned, standard) */}
        {(!selectedRows || selectedRows.length === 0) && (
          <div className="flex justify-end mb-2">
            <button
              type="button"
              onClick={handleAddTicketForm}
              className="flex items-center justify-center w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600 shadow transition"
              title="Add Ticket"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        )}

        {/* Footer Buttons (fixed at bottom of modal) */}
        <div className="flex justify-end p-4 bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 space-x-4">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            className="bg-blue-600 hover:bg-blue-700 text-white font-semibold transition"
            onClick={handleFinalSubmit}
          >
            Create
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateTicketModal;
