"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/pms/manage_tickets/page",{

/***/ "(app-pages-browser)/./app/pms/manage_tickets/components/ticket-sidebar.tsx":
/*!**************************************************************!*\
  !*** ./app/pms/manage_tickets/components/ticket-sidebar.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TicketSidebar: function() { return /* binding */ TicketSidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./components/ui/sheet.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ExternalLink,Flag,MessageSquare,Tag!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _comment_section__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./comment-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/comment-section.tsx\");\n/* harmony import */ var _tag_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./tag-manager */ \"(app-pages-browser)/./app/pms/manage_tickets/components/tag-manager.tsx\");\n/* harmony import */ var _TicketContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../TicketContext */ \"(app-pages-browser)/./app/pms/manage_tickets/TicketContext.tsx\");\n/* harmony import */ var _activity_section__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./activity-section */ \"(app-pages-browser)/./app/pms/manage_tickets/components/activity-section.tsx\");\n/* __next_internal_client_entry_do_not_use__ TicketSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction TicketSidebar(param) {\n    let { ticket: initialTicket, isOpen, onClose, onOpenInNewTab, onTagsUpdated } = param;\n    var _ticket_pipeline, _ticket_pipeline1;\n    _s();\n    const [commentsCount, setCommentsCount] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(0);\n    const { users, currentUser, setTickets, tickets } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useContext)(_TicketContext__WEBPACK_IMPORTED_MODULE_9__.TicketContext);\n    // Get the latest ticket from context instead of using the initial prop\n    let ticket = tickets.find((t)=>t.id === (initialTicket === null || initialTicket === void 0 ? void 0 : initialTicket.id)) || initialTicket;\n    // Ensure currentStage has the pipeline stage name\n    if (ticket && ticket.currentStage && ((_ticket_pipeline = ticket.pipeline) === null || _ticket_pipeline === void 0 ? void 0 : _ticket_pipeline.stages)) {\n        const pipelineStage = ticket.pipeline.stages.find((ps)=>ps.id === ticket.currentStage.pipelineStageId);\n        if (pipelineStage && !ticket.currentStage.name) {\n            ticket = {\n                ...ticket,\n                currentStage: {\n                    ...ticket.currentStage,\n                    name: pipelineStage.name\n                }\n            };\n        }\n    }\n    // Fetch comments count immediately when sidebar opens for better UX\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        async function fetchCommentsCount() {\n            if (!ticket) return;\n            try {\n                var _data_data;\n                const res = await fetch(\"\".concat(\"http://localhost:5001\", \"/api/comments/ticket/\").concat(ticket.id, \"?context=sidebar-comments-count\"));\n                const data = await res.json();\n                setCommentsCount(((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.length) || 0);\n            } catch (e) {\n                setCommentsCount(0);\n            }\n        }\n        if (isOpen && ticket) {\n            fetchCommentsCount();\n        }\n    }, [\n        isOpen,\n        ticket\n    ]);\n    if (!ticket) return null;\n    const priorityColors = {\n        low: \"bg-gray-100 text-gray-800\",\n        medium: \"bg-blue-100 text-blue-800\",\n        high: \"bg-orange-100 text-orange-800\",\n        urgent: \"bg-red-100 text-red-800\"\n    };\n    // Use ticket.currentStage for consistency\n    const currentStage = ticket.currentStage;\n    // Use assignedUser from currentStage\n    const assignedUser = currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedUser;\n    let assignedToDisplay;\n    if (currentUser && ((currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.id || (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) === currentUser.username)) {\n        assignedToDisplay = \"You\";\n    } else if (assignedUser) {\n        assignedToDisplay = assignedUser.username || assignedUser.id;\n    } else {\n        assignedToDisplay = (currentStage === null || currentStage === void 0 ? void 0 : currentStage.assignedTo) || \"Unassigned\";\n    }\n    // Define a palette of badge color classes\n    const badgeColors = [\n        \"bg-gray-200 text-gray-800\",\n        \"bg-blue-200 text-blue-800\",\n        \"bg-green-200 text-green-800\",\n        \"bg-yellow-200 text-yellow-800\",\n        \"bg-purple-200 text-purple-800\",\n        \"bg-pink-200 text-pink-800\",\n        \"bg-orange-200 text-orange-800\",\n        \"bg-red-200 text-red-800\"\n    ];\n    // Find the index of the current stage in the pipeline's stages array\n    let stageColor = \"bg-gray-200 text-gray-800\";\n    if ((_ticket_pipeline1 = ticket.pipeline) === null || _ticket_pipeline1 === void 0 ? void 0 : _ticket_pipeline1.stages) {\n        const idx = ticket.pipeline.stages.findIndex((s)=>{\n            var _ticket_currentStage;\n            return s.id === ((_ticket_currentStage = ticket.currentStage) === null || _ticket_currentStage === void 0 ? void 0 : _ticket_currentStage.pipelineStageId);\n        });\n        if (idx !== -1) {\n            stageColor = badgeColors[idx % badgeColors.length];\n        }\n    }\n    const handleCommentAdded = ()=>{\n        setCommentsCount((prev)=>prev + 1);\n    };\n    const handleTagsUpdated = ()=>{\n        onTagsUpdated === null || onTagsUpdated === void 0 ? void 0 : onTagsUpdated();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.Sheet, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetContent, {\n            className: \"w-full sm:max-w-lg overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 pr-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_5__.SheetTitle, {\n                                        className: \"text-lg mb-2 leading-tight\",\n                                        children: ticket.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                className: priorityColors[ticket.priority],\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"mr-1 h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 131,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    ticket.priority\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                className: stageColor,\n                                                variant: \"secondary\",\n                                                children: currentStage.pipelineStageId\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, this),\n                                            ticket.tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                    className: \"\".concat(tag.color, \" text-xs\"),\n                                                    children: tag.tagName || tag.name\n                                                }, tag.id, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, this))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>onOpenInNewTab(ticket.id),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                        defaultValue: \"details\",\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                                className: \"grid w-full grid-cols-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"details\",\n                                        className: \"text-xs\",\n                                        children: \"Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"comments\",\n                                        className: \"text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Comments\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1 text-blue-600 font-bold\",\n                                                children: [\n                                                    \"(\",\n                                                    commentsCount,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"tags\",\n                                        className: \"text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-1 h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Tags\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                        value: \"activity\",\n                                        className: \"text-xs\",\n                                        children: \"Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"details\",\n                                className: \"space-y-6 mt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold mb-3\",\n                                                children: \"Description\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 text-sm leading-relaxed\",\n                                                children: ticket.description || \"No description provided\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Assigned To\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 182,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: assignedUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                                    className: \"h-5 w-5\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarImage, {\n                                                                            src: assignedUser.avatar || \" \",\n                                                                            alt: assignedToDisplay\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                            lineNumber: 187,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                                            className: \"text-xs\",\n                                                                            children: assignedUser.username ? assignedUser.username[0].toUpperCase() : \"\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                            lineNumber: 188,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                    lineNumber: 186,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: assignedToDisplay\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                    lineNumber: 192,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: assignedToDisplay\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            currentStage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Due Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ExternalLink_Flag_MessageSquare_Tag_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm \".concat(currentStage.dueAt && new Date(currentStage.dueAt) < new Date() ? \"text-red-600\" : \"\"),\n                                                                children: currentStage.dueAt ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(currentStage.dueAt), \"PPP\") : \"No due date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 202,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Created\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(ticket.createdAt), \"PPP\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium text-sm text-gray-500 mb-2\",\n                                                        children: \"Last Updated\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(ticket.updatedAt), \"PPP\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"comments\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_comment_section__WEBPACK_IMPORTED_MODULE_7__.CommentSection, {\n                                    ticketId: ticket.id,\n                                    createdBy: (currentUser === null || currentUser === void 0 ? void 0 : currentUser.username) || \"\",\n                                    setCommentsCount: setCommentsCount,\n                                    onCommentsChange: (newComments)=>{\n                                        setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                    ...t,\n                                                    comments: newComments\n                                                } : t));\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"tags\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tag_manager__WEBPACK_IMPORTED_MODULE_8__.TagManager, {\n                                    ticketId: ticket.id,\n                                    assignedTags: ticket.tags,\n                                    onTagsUpdated: handleTagsUpdated,\n                                    onTagsChange: (newTags)=>{\n                                        setTickets((prev)=>prev.map((t)=>t.id === ticket.id ? {\n                                                    ...t,\n                                                    tags: newTags\n                                                } : t));\n                                    },\n                                    createdBy: ticket.createdBy || \"\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                                value: \"activity\",\n                                className: \"space-y-4 mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_activity_section__WEBPACK_IMPORTED_MODULE_10__.ActivitySection, {\n                                    ticketId: ticket.id\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n            lineNumber: 124,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\pms-moon\\\\pms-moon\\\\client\\\\app\\\\pms\\\\manage_tickets\\\\components\\\\ticket-sidebar.tsx\",\n        lineNumber: 123,\n        columnNumber: 5\n    }, this);\n}\n_s(TicketSidebar, \"/JlHD3ULYJrvNAIPUVxTNkK2Oks=\");\n_c = TicketSidebar;\nvar _c;\n$RefreshReg$(_c, \"TicketSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/pms/manage_tickets/components/ticket-sidebar.tsx\n"));

/***/ })

});