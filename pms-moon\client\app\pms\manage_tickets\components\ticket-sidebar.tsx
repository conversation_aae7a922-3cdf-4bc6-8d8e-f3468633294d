"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { She<PERSON>, She<PERSON><PERSON>ontent, Sheet<PERSON>eader, SheetTitle } from "@/components/ui/sheet"
import { ExternalLink, Calendar, Flag, MessageSquare, Tag } from "lucide-react"
import { format } from "date-fns"
import { Ticket } from "../ticket"
import { getAllData } from "@/lib/helpers";
import { employee_routes, comment_routes } from "@/lib/routePath";
import { useContext, useState, useEffect } from "react";
import { CommentSection } from "./comment-section"
import { TagManager } from "./tag-manager"
import { TicketContext } from "../TicketContext";
import { ActivitySection } from "./activity-section"

interface TicketSidebarProps {
  ticket: Ticket | null
  isOpen: boolean
  onClose: () => void
  onOpenInNewTab: (ticketId: string) => void
  onTagsUpdated?: () => void
}

export function TicketSidebar({ ticket: initialTicket, isOpen, onClose, onOpenInNewTab, onTagsUpdated }: TicketSidebarProps) {
  const [commentsCount, setCommentsCount] = useState(0)
  const [activeTab, setActiveTab] = useState<string>("details")
  const { users, currentUser, setTickets, tickets } = useContext(TicketContext);

  // Get the latest ticket from context instead of using the initial prop
  let ticket = tickets.find(t => t.id === initialTicket?.id) || initialTicket;

  // Ensure currentStage has the pipeline stage name
  if (ticket && ticket.currentStage && ticket.pipeline?.stages) {
    const pipelineStage = ticket.pipeline.stages.find(
      ps => ps.id === ticket.currentStage.pipelineStageId
    );
    if (pipelineStage && !ticket.currentStage.name) {
      ticket = {
        ...ticket,
        currentStage: {
          ...ticket.currentStage,
          name: pipelineStage.name
        }
      };
    }
  }

  // Fetch comments count immediately when sidebar opens for better UX
  useEffect(() => {
    async function fetchCommentsCount() {
      if (!ticket) return;
      try {
        const res = await fetch(comment_routes.GET_COMMENTS_BY_TICKET(ticket.id));
        const data = await res.json();
        setCommentsCount(data.data?.length || 0);
      } catch (e) {
        setCommentsCount(0);
      }
    }

    if (isOpen && ticket) {
      fetchCommentsCount();
    }
  }, [isOpen, ticket]);

  if (!ticket) return null

  const priorityColors = {
    low: "bg-gray-100 text-gray-800",
    medium: "bg-blue-100 text-blue-800",
    high: "bg-orange-100 text-orange-800",
    urgent: "bg-red-100 text-red-800",
  }

  // Use ticket.currentStage for consistency
  const currentStage = ticket.currentStage;
  // Use assignedUser from currentStage
  const assignedUser = currentStage?.assignedUser;
  let assignedToDisplay;
  if (
    currentUser &&
    (currentStage?.assignedTo === currentUser.id || currentStage?.assignedTo === currentUser.username)
  ) {
    assignedToDisplay = "You";
  } else if (assignedUser) {
    assignedToDisplay = assignedUser.username || assignedUser.id;
  } else {
    assignedToDisplay = currentStage?.assignedTo || "Unassigned";
  }

  // Define a palette of badge color classes
  const badgeColors = [
    "bg-gray-200 text-gray-800",
    "bg-blue-200 text-blue-800",
    "bg-green-200 text-green-800",
    "bg-yellow-200 text-yellow-800",
    "bg-purple-200 text-purple-800",
    "bg-pink-200 text-pink-800",
    "bg-orange-200 text-orange-800",
    "bg-red-200 text-red-800",
  ];
  // Find the index of the current stage in the pipeline's stages array
  let stageColor = "bg-gray-200 text-gray-800";
  if (ticket.pipeline?.stages) {
    const idx = ticket.pipeline.stages.findIndex(
      s => s.id === ticket.currentStage?.pipelineStageId
    );
    if (idx !== -1) {
      stageColor = badgeColors[idx % badgeColors.length];
    }
  }

  const handleCommentAdded = () => {
    setCommentsCount(prev => prev + 1)
  }

  const handleTagsUpdated = () => {
    onTagsUpdated?.()
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-full sm:max-w-lg overflow-y-auto">
        <SheetHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1 pr-4">
              <SheetTitle className="text-lg mb-2 leading-tight">{ticket.title}</SheetTitle>
              <div className="flex flex-wrap items-center gap-2">
                <Badge className={priorityColors[ticket.priority]}>
                  <Flag className="mr-1 h-3 w-3" />
                  {ticket.priority}
                </Badge>
                {currentStage && (
                  <Badge className={stageColor} variant="secondary">
                    {currentStage.pipelineStageId}
                  </Badge>
                )}
                {ticket.tags.map((tag) => (
                  <Badge key={tag.id} className={`${tag.color} text-xs`}>
                    {tag.tagName || tag.name}
                  </Badge>
                ))}
              </div>
            </div>
            <Button variant="outline" size="sm" onClick={() => onOpenInNewTab(ticket.id)}>
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </SheetHeader>

        <div className="mt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="details" className="text-xs">
                Details
              </TabsTrigger>
              <TabsTrigger value="comments" className="text-xs">
                <MessageSquare className="mr-1 h-3 w-3" />
                Comments
                <span className="ml-1 text-blue-600 font-bold">
                  ({commentsCount})
                </span>
              </TabsTrigger>
              <TabsTrigger value="tags" className="text-xs">
                <Tag className="mr-1 h-3 w-3" />
                Tags
              </TabsTrigger>
              <TabsTrigger value="activity" className="text-xs">
                Activity
              </TabsTrigger>
            </TabsList>

            <TabsContent value="details" className="space-y-6 mt-4">
              <div>
                <h3 className="font-semibold mb-3">Description</h3>
                <p className="text-gray-700 text-sm leading-relaxed">{ticket.description || "No description provided"}</p>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-sm text-gray-500 mb-2">Assigned To</h4>
                  <div className="flex items-center space-x-2">
                    {assignedUser ? (
                      <>
                        <Avatar className="h-5 w-5">
                          <AvatarImage src={assignedUser.avatar || " "} alt={assignedToDisplay} />
                          <AvatarFallback className="text-xs">
                            {assignedUser.username ? assignedUser.username[0].toUpperCase() : ""}
                          </AvatarFallback>
                        </Avatar>
                        <span>{assignedToDisplay}</span>
                      </>
                    ) : (
                      <span>{assignedToDisplay}</span>
                    )}
                  </div>
                </div>
                {currentStage && (
                  <div>
                    <h4 className="font-medium text-sm text-gray-500 mb-2">Due Date</h4>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span className={`text-sm ${currentStage.dueAt && new Date(currentStage.dueAt) < new Date() ? "text-red-600" : ""}`}>
                        {currentStage.dueAt ? format(new Date(currentStage.dueAt), "PPP") : "No due date"}
                      </span>
                    </div>
                  </div>
                )}
                <div>
                  <h4 className="font-medium text-sm text-gray-500 mb-2">Created</h4>
                  <p className="text-sm">{format(new Date(ticket.createdAt), "PPP")}</p>
                </div>

                <div>
                  <h4 className="font-medium text-sm text-gray-500 mb-2">Last Updated</h4>
                  <p className="text-sm">{format(new Date(ticket.updatedAt), "PPP")}</p>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="comments" className="space-y-4 mt-4">
              <CommentSection
                ticketId={ticket.id}
                createdBy={currentUser?.username || ""}
                setCommentsCount={setCommentsCount}
                onCommentsChange={(newComments) => {
                  setTickets(prev => prev.map(t => t.id === ticket.id ? { ...t, comments: newComments } : t));
                }}
                isActive={activeTab === "comments"}
              />
            </TabsContent>

            <TabsContent value="tags" className="space-y-4 mt-4">
              <TagManager
                ticketId={ticket.id}
                assignedTags={ticket.tags}
                onTagsUpdated={handleTagsUpdated}
                onTagsChange={(newTags) => {
                  setTickets(prev => prev.map(t => t.id === ticket.id ? { ...t, tags: newTags } : t));
                }}
                createdBy={ticket.createdBy || ""}
              />
            </TabsContent>

            <TabsContent value="activity" className="space-y-4 mt-4">
              <ActivitySection ticketId={ticket.id} />
            </TabsContent>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  )
}
