import prisma from "../../../utils/prismaClient";

export const deleteTicket = async (req: any, res: any) => {
    const id = req.params.id;
    const { deletedBy } = req.body;

    // Use frontend username or fallback to 'system'
    const username = deletedBy || "system";

    try {
        const existingRecord = await prisma.ticket.findUnique({
            where: { id: id },
        });

        if (!existingRecord) {
            return res
                .status(400)
                .json({ success: false, message: "Ticket not found" });
        }

        // Soft delete by updating deletedAt and deletedBy
        await prisma.ticket.update({
            where: { id: id },
            data: {
                deletedAt: new Date(),
                deletedBy: username,
            },
        });

        return res.status(200).json({
            success: true,
            message: "Ticket deleted successfully"
        });
    } catch (error) {
        return res.status(500).json({ success: false, message: error.message });
    }
};