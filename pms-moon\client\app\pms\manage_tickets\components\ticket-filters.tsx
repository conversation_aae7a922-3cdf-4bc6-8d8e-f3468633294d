"use client"

import { useState } from "react"
import type { TicketFilters as TicketFiltersType, User, Tag, Priority } from "../ticket"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { Checkbox } from "@/components/ui/checkbox"
import { Search, Filter, X, CalendarIcon, Users, TagIcon } from "lucide-react"
import { format } from "date-fns"
import Select from "react-select"
import { DateRange } from 'react-date-range';
import 'react-date-range/dist/styles.css'; // main style file
import 'react-date-range/dist/theme/default.css'; // theme css file
import { fetchTags } from "../tickets"

interface TicketFiltersProps {
  filters: TicketFiltersType
  onFiltersChange: (filters: TicketFiltersType) => void
  users: User[]
  stages: { id: string; name: string }[]
}

const priorityOptions: { value: Priority; label: string }[] = [
  { value: "low", label: "Low" },
  { value: "medium", label: "Medium" },
  { value: "high", label: "High" },
]

export function TicketFilters({ filters, onFiltersChange, users, stages }: Omit<TicketFiltersProps, 'tags'>) {
  const [showFilters, setShowFilters] = useState(false)
  const [tags, setTags] = useState<Tag[]>([]);
  const [tagsLoaded, setTagsLoaded] = useState(false);
  const handleTagDropdownOpen = async () => {
    if (!tagsLoaded) {
      const allTags = await fetchTags();
      setTags(allTags);
      setTagsLoaded(true);
    }
  };

  const updateFilters = (updates: Partial<TicketFiltersType>) => {
    onFiltersChange({ ...filters, ...updates })
  }

  const clearFilters = () => {
    onFiltersChange({
      search: "",
      stageIds: [],
      priority: [],
      tags: [],
      dateRange: {},
      assignedTo: []
    })
  }

  const hasActiveFilters =
    filters.search ||
    filters.stageIds.length > 0 ||
    filters.priority.length > 0 ||
    filters.tags.length > 0 ||
    filters.dateRange.from ||
    filters.dateRange.to

  return (
    <div className="space-y-4">
      {/* Search and Filter Toggle */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search tickets..."
            value={filters.search}
            onChange={(e) => updateFilters({ search: e.target.value })}
            className="pl-10"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className={hasActiveFilters ? "border-blue-500 text-blue-600" : ""}
          >
            <Filter className="mr-2 h-4 w-4" />
            Filters
            {hasActiveFilters && <Badge className="ml-2 bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5">Active</Badge>}
          </Button>

          {hasActiveFilters && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              <X className="mr-1 h-4 w-4" />
              Clear
            </Button>
          )}
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="bg-white border rounded-lg p-4 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Stage Filter */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Stage</label>
              <div className="space-y-2">
                {stages.map((stage) => (
                  <div key={stage.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`stage-${stage.id}`}
                      checked={filters.stageIds.includes(stage.id)}
                      onCheckedChange={(checked) => {
                        const newStageIds = checked
                          ? [...filters.stageIds, stage.id]
                          : filters.stageIds.filter((s) => s !== stage.id)
                        updateFilters({ stageIds: newStageIds })
                      }}
                    />
                    <label htmlFor={`stage-${stage.id}`} className="text-sm">
                      {stage.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Priority Filter */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">Priority</label>
              <div className="space-y-2">
                {priorityOptions.map((priority) => (
                  <div key={priority.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`priority-${priority.value}`}
                      checked={filters.priority.includes(priority.value)}
                      onCheckedChange={(checked) => {
                        const newPriority = checked
                          ? [...filters.priority, priority.value]
                          : filters.priority.filter((p) => p !== priority.value)
                        updateFilters({ priority: newPriority })
                      }}
                    />
                    <label htmlFor={`priority-${priority.value}`} className="text-sm">
                      {priority.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Tags Filter */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <TagIcon className="mr-1 h-4 w-4" />
                Tags
              </label>
              <Select
                isMulti
                options={tags.map(tag => ({
                  value: tag.id,
                  label: tag.name || tag.tagName || tag.id
                }))}
                value={tags.filter(tag => filters.tags.includes(tag.id)).map(tag => ({
                  value: tag.id,
                  label: tag.name || tag.tagName || tag.id
                }))}
                onChange={selected => {
                  updateFilters({ tags: selected.map((s: any) => s.value) });
                }}
                classNamePrefix="react-select"
                placeholder="Select tags..."
                styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
                onMenuOpen={handleTagDropdownOpen}
              />
            </div>

            {/* Assigned To Filter */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                <Users className="mr-1 h-4 w-4" />
                Assigned To
              </label>
              <Select
                isMulti
                options={users.map(user => ({
                  value: String(user.id),
                  label: user.username
                }))}
                value={users.filter(user => filters.assignedTo.includes(String(user.id))).map(user => ({
                  value: String(user.id),
                  label: user.username
                }))}
                onChange={selected => {
                  updateFilters({ assignedTo: selected.map((s: any) => s.value) });
                }}
                classNamePrefix="react-select"
                placeholder="Select users..."
                styles={{ menu: base => ({ ...base, zIndex: 9999 }) }}
              />
            </div>
          </div>

          {/* Date Range Filter */}
          <div>
            <div className="flex items-center mb-2">
              <CalendarIcon className="mr-2 h-4 w-4 text-gray-400" />
              <label className="text-sm font-medium text-gray-700">Due Date Range</label>
            </div>
            <Popover>
              <PopoverTrigger asChild>
                <button
                  type="button"
                  className="w-80 flex items-center justify-between px-4 py-2 border rounded-lg bg-white text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <span className="flex items-center">
                    <CalendarIcon className="mr-2 h-4 w-4 text-gray-400" />
                    {filters.dateRange.from && filters.dateRange.to
                      ? `${format(filters.dateRange.from, "PPP")} – ${format(filters.dateRange.to, "PPP")}`
                      : "Select date range"}
                  </span>
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 mt-2 max-w-xs" align="start">
                <DateRange
                  ranges={[{
                    startDate: filters.dateRange.from || null,
                    endDate: filters.dateRange.to || null,
                    key: 'selection'
                  }]}
                  onChange={item => {
                    updateFilters({
                      dateRange: {
                        from: item.selection.startDate,
                        to: item.selection.endDate
                      }
                    });
                  }}
                  moveRangeOnFirstSelection={false}
                  showDateDisplay={false}
                  rangeColors={["#2563eb"]}
                  direction="vertical"
                  months={1}
                  editableDateInputs={true}
                  className="rounded-lg shadow border"
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
      )}
    </div>
  )
}
