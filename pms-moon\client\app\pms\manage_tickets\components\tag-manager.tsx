"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Plus, Tag as TagIcon, X, Search } from "lucide-react"
import { tag_routes } from "@/lib/routePath"
import { toast } from "react-hot-toast"
import { Tag as TagType } from "../ticket"
import { formSubmit, getAllData } from "@/lib/helpers"

interface TagManagerProps {
  ticketId: string
  assignedTags: TagType[]
  onTagsUpdated: () => void
  onTagsChange?: (tags: TagType[]) => void
  createdBy: string
}

// Professional color palette
const TAG_COLORS = [
  "bg-gray-100 text-gray-800",
  "bg-blue-100 text-blue-800", 
  "bg-green-100 text-green-800",
  "bg-yellow-100 text-yellow-800",
  "bg-purple-100 text-purple-800",
  "bg-pink-100 text-pink-800",
  "bg-orange-100 text-orange-800",
  "bg-red-100 text-red-800",
  "bg-indigo-100 text-indigo-800",
  "bg-teal-100 text-teal-800",
  "bg-cyan-100 text-cyan-800",
  "bg-emerald-100 text-emerald-800",
  "bg-amber-100 text-amber-800",
  "bg-rose-100 text-rose-800",
  "bg-violet-100 text-violet-800"
]

export function TagManager({ ticketId, assignedTags, onTagsUpdated, onTagsChange, createdBy }: TagManagerProps) {
  const [allTags, setAllTags] = useState<TagType[]>([])
  const [isCreatingTag, setIsCreatingTag] = useState(false)
  const [newTagName, setNewTagName] = useState("")
  const [isAssigningTags, setIsAssigningTags] = useState(false)
  const [localAssignedTags, setLocalAssignedTags] = useState<TagType[]>(assignedTags)
  const [searchTerm, setSearchTerm] = useState("")

  // Ref to prevent double API calls in React Strict Mode
  const hasLoadedTags = useRef(false)

  // Sync localAssignedTags with prop
  useEffect(() => {
    setLocalAssignedTags(assignedTags)
  }, [assignedTags])

  // Load all available tags
  useEffect(() => {
    // Prevent double API calls in React Strict Mode
    if (!hasLoadedTags.current) {
      hasLoadedTags.current = true
      fetchAllTags()
    }
  }, [])

  const fetchAllTags = async () => {
    try {
      const data = await getAllData(tag_routes.GET_ALL_TAGS)
      setAllTags(data.data || [])
    } catch (error) {
      console.error("Error fetching tags:", error)
      toast.error("Failed to fetch tags")
    }
  }

  const handleCreateTag = async () => {
    if (!newTagName.trim()) {
      toast.error("Please enter a tag name")
      return
    }
    setIsCreatingTag(true)
    try {
      const result = await formSubmit(
        tag_routes.CREATE_TAG,
        "POST",
        {
          tagName: newTagName.trim(),
          color: TAG_COLORS[Math.floor(Math.random() * TAG_COLORS.length)],
          createdBy: createdBy,
        }
      )
      if (result && result.success) {
        setNewTagName("")
        // Add the new tag to local state immediately
        const newTag = result.data
        if (newTag) {
          setAllTags(prev => [...prev, newTag])
        }
        toast.success("Tag created successfully. Click to assign it to this ticket.")
      } else {
        toast.error(result?.message || "Failed to create tag")
      }
    } catch (error) {
      console.error("Error creating tag:", error)
      toast.error("Failed to create tag")
    } finally {
      setIsCreatingTag(false)
    }
  }

  const handleAssignTag = async (tagId: string) => {
    setIsAssigningTags(true)
    try {
      const result = await formSubmit(
        tag_routes.ASSIGN_TAGS_TO_TICKET,
        "POST",
        {
          ticketId,
          tagIds: [...localAssignedTags.map(t => t.id), tagId],
          createdBy: createdBy,
        }
      )
      if (result && result.success) {
        const tagObj = allTags.find(t => t.id === tagId)
        if (tagObj) {
          const newTags = [...localAssignedTags, tagObj]
          setLocalAssignedTags(newTags)
          // Update parent component immediately for smooth UX
          if (onTagsChange) {
            onTagsChange(newTags)
          }
        }
        toast.success("Tag assigned successfully")
      } else {
        toast.error(result?.message || "Failed to assign tag")
      }
    } catch (error) {
      console.error("Error assigning tag:", error)
      toast.error("Failed to assign tag")
    } finally {
      setIsAssigningTags(false)
    }
  }

  const handleUnassignTag = async (tagId: string) => {
    setIsAssigningTags(true)
    try {
      const result = await formSubmit(
        tag_routes.ASSIGN_TAGS_TO_TICKET,
        "POST",
        {
          ticketId,
          tagIds: localAssignedTags.filter(t => t.id !== tagId).map(t => t.id),
          createdBy: createdBy,
        }
      )
      if (result && result.success) {
        const newTags = localAssignedTags.filter(t => t.id !== tagId)
        setLocalAssignedTags(newTags)
        // Update parent component immediately for smooth UX
        if (onTagsChange) {
          onTagsChange(newTags)
        }
        toast.success("Tag unassigned successfully")
      } else {
        toast.error(result?.message || "Failed to unassign tag")
      }
    } catch (error) {
      console.error("Error unassigning tag:", error)
      toast.error("Failed to unassign tag")
    } finally {
      setIsAssigningTags(false)
    }
  }

  // Filter available tags based on search term
  const filteredAvailableTags = allTags.filter(tag => 
    !localAssignedTags.some(a => a.id === tag.id) &&
    tag.tagName.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-4">
      {/* Add New Tag */}
      <div className="border rounded-lg p-4 bg-gray-50">
        <h4 className="font-medium text-sm mb-3">Add New Tag</h4>
        <div className="flex space-x-2">
          <Input
            placeholder="Tag name..."
            className="flex-1"
            value={newTagName}
            onChange={(e) => setNewTagName(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === "Enter" && newTagName.trim()) {
                handleCreateTag()
              }
            }}
          />
          <Button
            size="sm"
            onClick={handleCreateTag}
            disabled={isCreatingTag || !newTagName.trim()}
          >
            <Plus className="h-4 w-4 mr-1" />
            {isCreatingTag ? "Adding..." : "Add"}
          </Button>
        </div>
      </div>

      {/* Current Tags */}
      <div>
        <h4 className="font-medium text-sm text-gray-500 mb-3">Current Tags</h4>
        {localAssignedTags.length === 0 ? (
          <p className="text-gray-500 text-sm">No tags assigned</p>
        ) : (
          <div className="flex flex-wrap gap-2">
            {localAssignedTags.map((tag) => (
              <div key={tag.id} className="group relative">
                <Badge className={`${tag.color} pr-6`} variant="secondary">
                  {tag.tagName}
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 bg-red-100 hover:bg-red-200 rounded-full"
                  onClick={() => handleUnassignTag(tag.id)}
                  disabled={isAssigningTags}
                >
                  <X className="h-3 w-3 text-red-600" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Available Tags */}
      <div>
        <h4 className="font-medium text-sm text-gray-500 mb-3">Available Tags</h4>
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input 
            placeholder="Search available tags..." 
            className="pl-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
          {filteredAvailableTags.length === 0 ? (
            <p className="text-gray-500 text-sm">
              {searchTerm ? "No tags match your search" : "No available tags"}
            </p>
          ) : (
            filteredAvailableTags.map((tag) => (
              <Button
                key={tag.id}
                variant="outline"
                size="sm"
                className="h-auto p-1 bg-transparent"
                onClick={() => handleAssignTag(tag.id)}
                disabled={isAssigningTags}
              >
                <Badge className={tag.color} variant="secondary">
                  {tag.tagName}
                </Badge>
                <Plus className="h-3 w-3 ml-1" />
              </Button>
            ))
          )}
        </div>
      </div>
    </div>
  )
} 