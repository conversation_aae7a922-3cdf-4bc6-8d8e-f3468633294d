"use client"

import { useState, useEffect, useContext, useRef } from "react"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Calendar, Flag, MessageSquare, RefreshCw } from "lucide-react"
import { format } from "date-fns"
import { Ticket } from "../ticket"
import { fetchTicket, fetchUsers } from "../tickets"
import { getAllData } from "@/lib/helpers"
import { employee_routes, comment_routes } from "@/lib/routePath"
import { CommentSection } from "../components/comment-section"
import { TagManager } from "../components/tag-manager"
import { TicketContext } from "../TicketContext"
import { ActivitySection } from "../components/activity-section"

export default function TicketDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [ticket, setTicket] = useState<Ticket | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<string>("details")
  const { users, currentUser, tickets, setTickets, setUsers } = useContext(TicketContext);
  const [commentsCount, setCommentsCount] = useState(ticket?.comments?.length ?? 0);

  // Refs to prevent double API calls in React Strict Mode
  const hasLoadedTicket = useRef<string | null>(null)
  const hasLoadedUsers = useRef(false)
  const hasLoadedCommentsCount = useRef<string | null>(null)

  useEffect(() => {
    const loadTicket = async () => {
      try {
        const data = await fetchTicket(params.id as string);
        // Patch: Construct currentStage if missing
        if (data && !data.currentStage && data.currentStageId && Array.isArray(data.stages)) {
          data.currentStage = data.stages.find(
            s => s.pipelineStageId === data.currentStageId
          );
        }

        // Ensure currentStage has the pipeline stage name
        if (data && data.currentStage && data.pipeline?.stages) {
          const pipelineStage = data.pipeline.stages.find(
            ps => ps.id === data.currentStage.pipelineStageId
          );
          if (pipelineStage) {
            data.currentStage.name = pipelineStage.name;
          }
        }
        setTicket(data);
        // Update the ticket in context as well
        setTickets(prev => {
          const idx = prev.findIndex(t => t.id === data.id);
          if (idx !== -1) {
            // Replace existing ticket
            const updated = [...prev];
            updated[idx] = data;
            return updated;
          } else {
            // Add new ticket if not present
            return [...prev, data];
          }
        });
      } catch (error) {
        console.error("Failed to fetch ticket:", error)
      } finally {
        setIsLoading(false)
      }
    }

    // Prevent double API calls in React Strict Mode
    if (params.id && hasLoadedTicket.current !== params.id) {
      hasLoadedTicket.current = params.id as string;
      loadTicket()
    }
  }, [params.id, setTickets])

  useEffect(() => {
    // Prevent double API calls in React Strict Mode
    if (users.length === 0 && !hasLoadedUsers.current) {
      hasLoadedUsers.current = true;
      fetchUsers().then(fetchedUsers => {
        setUsers(fetchedUsers);
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch comments count immediately when ticket loads for better UX
  useEffect(() => {
    async function fetchCommentsCount() {
      if (!ticket) return;
      try {
        const res = await fetch(comment_routes.GET_COMMENTS_BY_TICKET(ticket.id));
        const data = await res.json();
        setCommentsCount(data.data?.length || 0);
      } catch (e) {
        setCommentsCount(0);
      }
    }

    // Only fetch comments count if ticket exists and we haven't already fetched for this ticket
    if (ticket && hasLoadedCommentsCount.current !== ticket.id) {
      hasLoadedCommentsCount.current = ticket.id;
      fetchCommentsCount();
    }
  }, [ticket]);

  const handleTagsUpdated = async () => {
    if (params.id) {
      const data = await fetchTicket(params.id as string)
      setTicket(data)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!ticket) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Ticket not found</h1>
        <p className="text-gray-600 mb-4">The ticket you're looking for doesn't exist.</p>
        <Button onClick={() => router.push("/pms/manage_tickets")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Tickets
        </Button>
      </div>
    )
  }

  if (users.length === 0) {
    return <div>Loading users...</div>;
  }

  const currentStage = ticket.currentStage;
  // Use assignedUser from currentStage
  const assignedUser = currentStage?.assignedUser;
  let assignedToDisplay;
  if (
    currentUser &&
    (currentStage?.assignedTo === currentUser.id || currentStage?.assignedTo === currentUser.username)
  ) {
    assignedToDisplay = "You";
  } else if (assignedUser) {
    assignedToDisplay = assignedUser.username || assignedUser.id;
  } else {
    assignedToDisplay = currentStage?.assignedTo || "Unassigned";
  }

  // Assignee: use createdBy (for legacy)
  const assigneeDisplay = ticket.createdBy || "Unassigned";
  // Owner: use owner
  const ownerDisplay = ticket.owner || "";

  const priorityColors = {
    Low: "bg-gray-100 text-gray-800 hover:bg-gray-50",
    Medium: "bg-blue-100 text-blue-800 hover:bg-blue-50",
    High: "bg-orange-100 text-orange-800 hover:bg-orange-50",
    Urgent: "bg-red-100 text-red-800 hover:bg-red-50",
    // Also support lowercase versions for compatibility
    low: "bg-gray-100 text-gray-800 hover:bg-gray-50",
    medium: "bg-blue-100 text-blue-800 hover:bg-blue-50",
    high: "bg-orange-100 text-orange-800 hover:bg-orange-50",
    urgent: "bg-red-100 text-red-800 hover:bg-red-50",
  }

  // Stage colors for consistent badge coloring (same as modal)
  const badgeColors = [
    "bg-blue-200 text-blue-800",
    "bg-green-200 text-green-800",
    "bg-yellow-200 text-yellow-800",
    "bg-purple-200 text-purple-800",
    "bg-indigo-200 text-indigo-800",
    "bg-pink-200 text-pink-800",
    "bg-orange-200 text-orange-800",
    "bg-red-200 text-red-800",
  ];

  // Calculate stage color based on pipeline stage index (same logic as modal)
  let stageColor = "bg-gray-200 text-gray-800";
  if (ticket.pipeline?.stages) {
    const idx = ticket.pipeline.stages.findIndex(
      s => s.id === ticket.currentStage?.pipelineStageId
    );
    if (idx !== -1) {
      stageColor = badgeColors[idx % badgeColors.length];
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <Button variant="ghost" onClick={() => router.push("/pms/manage_tickets")} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Tickets
          </Button>

          <div className="bg-white rounded-lg border p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 mb-3">{ticket.title}</h1>
                <div className="flex flex-wrap items-center gap-2">
                  <Badge className={priorityColors[ticket.priority]}>
                    <Flag className="mr-1 h-3 w-3" />
                    {ticket.priority}
                  </Badge>
                  {/* Show current stage name as badge instead of ID */}
                  {currentStage && (
                    <Badge variant="secondary" className={stageColor}>
                      {currentStage.name}
                    </Badge>
                  )}
                  {(ticket.tags || []).map((tag) => (
                    <Badge key={tag.id} className={tag.color} variant="secondary">
                      {tag.tagName || tag.name}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="details">Details</TabsTrigger>
                <TabsTrigger value="comments">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Comments
                  <span className="ml-1 text-blue-600 font-bold">
                    ({commentsCount})
                  </span>
                </TabsTrigger>
                <TabsTrigger value="tags">Tags</TabsTrigger>
                <TabsTrigger value="activity">Activity</TabsTrigger>
              </TabsList>

              <TabsContent value="details" className="space-y-6 mt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-3">Description</h3>
                    <p className="text-gray-700 leading-relaxed">{ticket.description || "No description provided"}</p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-2">Assigned To</h4>
                      <div className="flex items-center space-x-2">
                        {assignedUser ? (
                          <>
                            <Avatar className="h-5 w-5">
                              <AvatarImage src={assignedUser.avatar || " "} alt={assignedToDisplay} />
                              <AvatarFallback className="text-xs">
                                {assignedUser.username ? assignedUser.username[0].toUpperCase() : ""}
                              </AvatarFallback>
                            </Avatar>
                            <span>{assignedToDisplay}</span>
                          </>
                        ) : (
                          <span>{assignedToDisplay}</span>
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-2">Owner</h4>
                      <p className="text-gray-700 text-sm">{ownerDisplay}</p>
                    </div>

                    {currentStage && (
                      <div>
                        <h4 className="font-medium text-sm text-gray-500 mb-2">Due Date</h4>
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span className={`text-sm ${currentStage.dueAt && new Date(currentStage.dueAt) < new Date() ? "text-red-600" : ""}`}>
                            {currentStage.dueAt ? format(new Date(currentStage.dueAt), "PPP") : "No due date"}
                          </span>
                        </div>
                      </div>
                    )}

                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-2">Created</h4>
                      <p className="text-sm">{format(new Date(ticket.createdAt), "PPP")}</p>
                    </div>

                    <div>
                      <h4 className="font-medium text-sm text-gray-500 mb-2">Last Updated</h4>
                      <p className="text-sm">{format(new Date(ticket.updatedAt), "PPP")}</p>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="comments" className="space-y-4 mt-6">
                <CommentSection
                  ticketId={ticket.id}
                  createdBy={currentUser?.username || ""}
                  setCommentsCount={setCommentsCount}
                  onCommentsChange={(newComments) => {
                    setTickets(prev => prev.map(t => t.id === ticket.id ? { ...t, comments: newComments } : t));
                  }}
                  isActive={activeTab === "comments"}
                />
              </TabsContent>

              <TabsContent value="tags" className="space-y-4 mt-6">
                <TagManager
                  ticketId={ticket.id}
                  assignedTags={ticket.tags}
                  onTagsUpdated={handleTagsUpdated}
                  onTagsChange={(newTags) => {
                    setTickets(prev => prev.map(t => t.id === ticket.id ? { ...t, tags: newTags } : t));
                  }}
                  createdBy={ticket.createdBy || ""}
                />
              </TabsContent>

              <TabsContent value="activity" className="space-y-4 mt-6">
                <ActivitySection ticketId={ticket.id} />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </div>
  )
}
