"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/ticket/create");
const view_1 = require("../../controllers/ticket/view");
const delete_1 = require("../../controllers/ticket/delete");
const update_1 = require("../../controllers/ticket/update");
const router = (0, express_1.Router)();
// More specific routes first
router.put("/ticket/:id", 
// authenticate,
update_1.ticketUpdate);
router.get("/:id/stage-logs", 
// authenticate,
view_1.getTicketStageChangeLogs);
router.put("/bulk", 
// authenticate,
update_1.bulkUpdateTickets);
// Generic routes last
router.post("/", 
// authenticate,
create_1.createTicket);
router.get("/", 
//authenticate,
view_1.viewTicket);
router.get("/:id", 
//authenticate,
view_1.viewTicketById);
router.delete("/:id", 
// authenticate,
delete_1.deleteTicket);
exports.default = router;
//# sourceMappingURL=ticketRoutes.js.map