import { Inter } from "next/font/google";
import { Toaster } from "@/components/ui/sonner";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";

import { AdminNavBar } from "@/components/adminNavBar/adminNavBar";
import { getAllData, getCookie } from "@/lib/helpers";
import { employee_routes } from "@/lib/routePath";
import Sidebar from "@/components/sidebar/Sidebar";
import { TicketProvider } from "./manage_tickets/TicketContext";


const inter = Inter({ subsets: ["latin"] });

export const metadata = {
  title: "Dashboard",
};

export default async function  AdminLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const currentUser = await getAllData(employee_routes.GETCURRENT_USER)
  
  const userPermissions = currentUser?.role?.role_permission || [];
//  (currentUser)
  const adminCookie = await getCookie("corporationtoken");
  const permission = adminCookie ? ["allow_all"] : userPermissions;
  //  (adminCookie)
  //  (permission)

  // Always wrap in TicketProvider for all /pms pages
  return (
    <SidebarProvider>
      <Sidebar permissions={permission} profile={currentUser} />
      {/* <main className="pt-1">
        <SidebarTrigger />
      </main> */}
      <TicketProvider initialCurrentUser={currentUser}>
        <div className={`${inter.className} flex-1 min-w-0`}>
          {children}
          <Toaster position="top-right" richColors />
        </div>
      </TicketProvider>
    </SidebarProvider>
  );
}
