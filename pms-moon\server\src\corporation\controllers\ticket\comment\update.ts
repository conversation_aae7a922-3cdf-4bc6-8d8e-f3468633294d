import prisma from "../../../../utils/prismaClient";
import { handleError } from "../../../../utils/helpers";

export const updateComment = async (req: any, res: any) => {
  try {
    const { id: commentId } = req.params;
    const { content, updatedBy } = req.body;
    
    if (!commentId || !content) {
      return res.status(400).json({
        success: false,
        message: "Comment ID and content are required",
      });
    }

    // Only use updatedBy from req.body or fallback to 'system'
    let username = updatedBy || "system";

    const comment = await prisma.comment.update({
      where: { id: commentId },
      data: {
        content,
        updatedBy: username,
      },
    });

    return res.status(200).json({
      success: true,
      message: "Comment updated successfully",
      data: comment,
    });
  } catch (error) {
    console.error("Error updating comment:", error);
    return handleError(res, error);
  }
}; 