import { Tag, Ticket, TicketStatus, User } from "./ticket"
import { ticket_routes, employee_routes, tag_routes } from "@/lib/routePath"

// Fetch all tickets
export async function fetchTickets(): Promise<Ticket[]> {
  const res = await fetch(ticket_routes.GET_TICKETS, { method: "GET" })
  if (!res.ok) throw new Error("Failed to fetch tickets")
  const data = await res.json()
  // Map API response to UI Ticket interface
  return data.data.map((apiTicket: any) => {
    // Find the correct current stage object
    let currentStage = undefined;
    if (apiTicket.currentStageId && Array.isArray(apiTicket.stages)) {
      currentStage = apiTicket.stages.find(
        (stage: any) => stage.pipelineStageId === apiTicket.currentStageId
      );
    } else if (apiTicket.currentStage) {
      currentStage = apiTicket.currentStage;
    } else if (Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0) {
      currentStage = apiTicket.stages[apiTicket.stages.length - 1];
    }
    return {
      id: apiTicket.id,
      title: apiTicket.title || `Ticket for Invoice ${apiTicket.workItemId}`,
      description: apiTicket.description || "",
      status: "todo", // Default to 'todo' if not present
      priority: (apiTicket.priority || "low").toLowerCase(),
      dueDate: Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0
        ? apiTicket.stages[apiTicket.stages.length - 1].dueAt
        : null,
      createdAt: apiTicket.createdAt ? new Date(apiTicket.createdAt) : new Date(),
      updatedAt: apiTicket.updatedAt ? new Date(apiTicket.updatedAt) : new Date(),
      tags: apiTicket.tags || [],
      comments: apiTicket.comments || [],
      pipeline: apiTicket.pipeline || {},
      currentStage: currentStage,
      stages: apiTicket.stages || [],
      createdBy: apiTicket.createdBy || "",
      owner: apiTicket.owner || "",
    }
  })
}

// Fetch all users
export async function fetchUsers(): Promise<User[]> {
  const res = await fetch(employee_routes.GETALL_USERS, { method: "GET", credentials: "include" })
  if (!res.ok) throw new Error("Failed to fetch users")
  const data = await res.json()
  // Map id to string
  return data.data.map((user: any) => ({
    ...user,
    id: String(user.id),
  }));
}

// Fetch a single ticket by ID
export async function fetchTicket(id: string): Promise<Ticket | null> {
  const url = ticket_routes.GET_TICKET_BY_ID(id);
  console.log("🌐 Making API call to URL:", url);
  const res = await fetch(url, { method: "GET" })
  console.log("📡 API response status:", res.status, res.statusText);
  if (!res.ok) return null
  const data = await res.json()
  const apiTicket = data.data
  // Map tags to UI format
  const tags = (apiTicket.tags || []).map((tag: any) => ({
    id: tag.id,
    tagName: tag.tagName || tag.name,
    name: tag.tagName || tag.name,
    color: tag.color || "bg-gray-100 text-gray-800",
    createdBy: tag.createdBy,
    createdAt: tag.createdAt,
  }))
  // Map dueDate from last stage if available
  let dueDate = null
  if (Array.isArray(apiTicket.stages) && apiTicket.stages.length > 0) {
    dueDate = apiTicket.stages[apiTicket.stages.length - 1].dueAt || null
  }
  return {
    id: apiTicket.id,
    title: apiTicket.title || `Ticket for Invoice ${apiTicket.workItemId}`,
    description: apiTicket.description || "",
    status: apiTicket.status || "todo",
    priority: (apiTicket.priority || "low").toLowerCase(),
    dueDate: dueDate ? new Date(dueDate) : null,
    createdAt: apiTicket.createdAt ? new Date(apiTicket.createdAt) : new Date(),
    updatedAt: apiTicket.updatedAt ? new Date(apiTicket.updatedAt) : new Date(),
    tags,
    comments: apiTicket.comments || [],
    pipeline: apiTicket.pipeline || {},
    currentStage: apiTicket.currentStage || undefined,
    currentStageId: apiTicket.currentStageId,
    stages: apiTicket.stages || [],
    createdBy: apiTicket.createdBy || "",
    owner: apiTicket.owner || "",
  }
}

// Update a ticket (status or other fields)
export async function updateTicket(id: string, updates: Partial<Ticket>, username?: string): Promise<Ticket> {
  const payload = {
    ...updates,
    ticketId: id,
    ...(username && { updatedBy: username }),
  };
  
  const res = await fetch(ticket_routes.UPDATE_TICKET(id), {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(payload),
  })
  if (!res.ok) throw new Error("Failed to update ticket")
  const data = await res.json()
  return data.ticket || data.data
}

// Update only the status of a ticket
export async function updateTicketStatus(id: string, status: TicketStatus, username?: string): Promise<Ticket> {
  // The backend expects the full update body, so send only status if that's all that's changing
  return updateTicket(id, { status }, username)
}

// Delete a ticket by ID
export async function deleteTicket(id: string, username?: string): Promise<void> {
  const payload = username ? { deletedBy: username } : {};
  
  const res = await fetch(ticket_routes.DELETE_TICKET(id), { 
    method: "DELETE",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(payload),
  })
  if (!res.ok) throw new Error("Failed to delete ticket")
}

// Bulk delete tickets by looping over IDs
export async function bulkDeleteTickets(ids: string[], username?: string): Promise<void> {
  for (const id of ids) {
    await deleteTicket(id, username)
  }
}

// Fetch all tags
export async function fetchTags(): Promise<Tag[]> {
  const res = await fetch(tag_routes.GET_ALL_TAGS, { method: "GET" })
  if (!res.ok) throw new Error("Failed to fetch tags")
  const data = await res.json()
  return data.data
}
