'use client';

import React, { createContext, useState, useMemo } from "react";

export interface TicketContextType {
  tickets: any[];
  setTickets: React.Dispatch<React.SetStateAction<any[]>>;
  users: any[];
  setUsers: React.Dispatch<React.SetStateAction<any[]>>;
  currentUser: any;
  setCurrentUser: React.Dispatch<React.SetStateAction<any>>;
}

export const TicketContext = createContext<TicketContextType>({
  tickets: [],
  setTickets: () => {},
  users: [],
  setUsers: () => {},
  currentUser: null,
  setCurrentUser: () => {},
});

export function TicketProvider({ children, initialCurrentUser }: { children: React.ReactNode, initialCurrentUser?: any }) {
  const [tickets, setTickets] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [currentUser, setCurrentUser] = useState<any>(initialCurrentUser || null);

  const contextValue = useMemo(
    () => ({ tickets, setTickets, users, setUsers, currentUser, setCurrentUser }),
    [tickets, users, currentUser]
  );

  return (
    <TicketContext.Provider value={contextValue}>
      {children}
    </TicketContext.Provider>
  );
} 