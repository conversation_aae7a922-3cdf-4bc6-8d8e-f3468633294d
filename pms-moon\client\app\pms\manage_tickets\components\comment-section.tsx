"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { Send, MessageCircle, Plus, Pencil, Trash2 } from "lucide-react"
import { comment_routes } from "@/lib/routePath"
import { toast } from "react-hot-toast"
import React from "react"
import dayjs from "dayjs"
import relativeTime from "dayjs/plugin/relativeTime"
dayjs.extend(relativeTime)

interface Comment {
  id: string
  content: string
  createdBy: string
  createdAt: string
  updatedAt?: string
}

interface CommentSectionProps {
  ticketId: string
  createdBy: string
  setCommentsCount: (count: number) => void
  onCommentsChange?: (comments: Comment[]) => void
  isActive?: boolean // Add prop to control when to fetch comments
}

export function CommentSection({ ticketId, createdBy, setCommentsCount, onCommentsChange, isActive = true }: CommentSectionProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [newComment, setNewComment] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null)
  const [editingContent, setEditingContent] = useState("")
  const [isEditing, setIsEditing] = useState(false)
  const [isDeletingId, setIsDeletingId] = useState<string | null>(null)

  // Ref to prevent double API calls in React Strict Mode
  const hasLoadedComments = useRef<string | null>(null)

  // Fetch comments from backend
  const fetchComments = useCallback(async () => {
    try {
      const res = await fetch(comment_routes.GET_COMMENTS_BY_TICKET(ticketId))
      const data = await res.json()
      setComments(data.data || [])
      setCommentsCount(data.data?.length || 0)
    } catch (error) {
      setComments([])
      setCommentsCount(0)
    }
  }, [ticketId, setCommentsCount])

  useEffect(() => {
    // Only fetch comments when this component is actually active (Comments tab is active)
    // The parent components already fetch the count, so we only need to fetch full comments here
    if (isActive && hasLoadedComments.current !== ticketId) {
      hasLoadedComments.current = ticketId
      fetchComments()
    }
  }, [fetchComments, ticketId, isActive])

  const handleSubmitComment = async () => {
    if (!newComment.trim()) {
      toast.error("Please enter a comment")
      return
    }

    setIsSubmitting(true)
    try {
      const response = await fetch(comment_routes.CREATE_COMMENT, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ticketId,
          content: newComment.trim(),
          createdBy,
        }),
      })

      if (response.ok) {
        const data = await response.json()
        setNewComment("")
        setComments((prev) => [data.data, ...prev])
        setCommentsCount(comments.length + 1)
        if (onCommentsChange) onCommentsChange([data.data, ...comments])
        toast.success("Comment added successfully")
      } else {
        toast.error("Failed to add comment")
      }
    } catch (error) {
      console.error("Error adding comment:", error)
      toast.error("Failed to add comment")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSubmitComment()
    }
  }

  const handleEditComment = async () => {
    if (!editingContent.trim() || !editingCommentId) return
    setIsSubmitting(true)
    try {
      const response = await fetch(comment_routes.UPDATE_COMMENT(editingCommentId), {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ content: editingContent.trim(), updatedBy: createdBy }),
      })
      if (response.ok) {
        const data = await response.json()
        setEditingCommentId(null)
        setEditingContent("")
        setNewComment("")
        setComments((prev) => prev.map((c) => c.id === editingCommentId ? data.data : c))
        if (onCommentsChange) onCommentsChange(comments.map((c) => c.id === editingCommentId ? data.data : c))
        toast.success("Comment updated successfully")
      } else {
        toast.error("Failed to update comment")
      }
    } catch (error) {
      toast.error("Failed to update comment")
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeleteComment = async (id: string) => {
    setIsDeletingId(id)
    try {
      const response = await fetch(comment_routes.DELETE_COMMENT(id), {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ deletedBy: createdBy }),
      })
      if (response.ok) {
        setComments((prev) => prev.filter((c) => c.id !== id))
        setCommentsCount(comments.length - 1)
        if (onCommentsChange) onCommentsChange(comments.filter((c) => c.id !== id))
        toast.success("Comment deleted successfully")
      } else {
        toast.error("Failed to delete comment")
      }
    } catch (error) {
      toast.error("Failed to delete comment")
    } finally {
      setIsDeletingId(null)
    }
  }

  return (
    <div className="w-full flex flex-col h-full bg-white rounded-lg border border-gray-200">
      {/* Tab-like header */}
      <div className="flex items-center border-b px-6 py-3 bg-gray-50">
        <span className="font-semibold text-base text-gray-900 mr-4">Comments</span>
        <span className="bg-blue-100 text-blue-700 rounded-full px-2 py-0.5 text-xs font-semibold">{comments.length}</span>
      </div>
      {/* Comments List */}
      <div className="flex-1 px-6 py-4 space-y-6 overflow-y-auto max-h-[240px]">
        {comments.length === 0 ? (
          <div className="text-center text-gray-400 py-8">No comments yet. Be the first to comment!</div>
        ) : (
          comments.map((comment) => (
            <div key={comment.id} className="flex items-start gap-3 group">
              {/* Avatar */}
              <div className="flex-shrink-0">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={undefined} alt={comment.createdBy} />
                  <AvatarFallback className="bg-blue-100 text-blue-700 font-bold">
                    {comment.createdBy.split(" ").map((n) => n[0]).join("").toUpperCase()}
                  </AvatarFallback>
                </Avatar>
              </div>
              {/* Bubble */}
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-0.5">
                  <span className="font-semibold text-gray-900 text-sm">{comment.createdBy}</span>
                  <span className="text-xs text-gray-400">· {dayjs(comment.updatedAt || comment.createdAt).fromNow()}</span>
                  {comment.updatedAt && comment.updatedAt !== comment.createdAt && (
                    <Badge variant="outline" className="text-[10px] px-1 py-0.5 bg-blue-100 text-blue-700 border-blue-200 ml-1">Edited</Badge>
                  )}
                </div>
                {editingCommentId === comment.id ? (
                  <div className="flex items-end gap-2 mt-1">
                    <Textarea
                      value={editingContent}
                      onChange={(e) => setEditingContent(e.target.value)}
                      className="min-h-[36px] text-xs resize-none px-2 py-1 border rounded-lg"
                      autoFocus
                    />
                    <Button onClick={handleEditComment} disabled={isSubmitting || !editingContent.trim()} size="icon" className="h-8 w-8">
                      <Send className="h-4 w-4" />
                    </Button>
                    <Button onClick={() => { setEditingCommentId(null); setEditingContent(""); setNewComment(""); }} disabled={isSubmitting} size="icon" variant="ghost" className="h-8 w-8">
                      Cancel
                    </Button>
                  </div>
                ) : (
                  <div className="bg-gray-100 rounded-xl px-4 py-2 text-sm text-gray-800 whitespace-pre-wrap">
                    {/* Highlight @mentions in blue */}
                    {comment.content.split(/(\s+)/).map((word, i) =>
                      word.startsWith("@") ? (
                        <span key={i} className="text-blue-600 font-medium">{word}</span>
                      ) : word
                    )}
                  </div>
                )}
                {/* Edit/Delete icons */}
                <div className="flex gap-2 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-150">
                  <button
                    className="text-blue-600 p-1"
                    title="Edit"
                    onClick={() => { setEditingCommentId(comment.id); setEditingContent(comment.content); setNewComment(comment.content); }}
                    disabled={isSubmitting || isDeletingId === comment.id}
                  >
                    <Pencil className="h-4 w-4" />
                  </button>
                  <button
                    className="text-red-600 p-1"
                    title="Delete"
                    onClick={() => handleDeleteComment(comment.id)}
                    disabled={isSubmitting || isDeletingId === comment.id}
                  >
                    {isDeletingId === comment.id ? (
                      <span className="animate-spin"><Trash2 className="h-4 w-4" /></span>
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
        {/* Divider and New Messages badge (UI only) */}
        {/* <div className="flex items-center my-4"><hr className="flex-1 border-gray-200" /><span className="mx-2 bg-blue-50 text-blue-600 rounded-full px-3 py-0.5 text-xs font-semibold">New Messages</span><hr className="flex-1 border-gray-200" /></div> */}
      </div>
      {/* Input Bar at Bottom */}
      <div className="border-t bg-white px-4 py-3 flex items-center gap-2">
        <Textarea
          placeholder="Add a comment..."
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          onKeyPress={handleKeyPress}
          className="flex-1 min-h-[28px] max-h-[32px] text-sm resize-none px-3 py-0 rounded-full border border-gray-200 bg-gray-50 focus:bg-white focus:border-blue-400 leading-tight placeholder:text-gray-400"
          style={{ height: '32px' }}
        />
        <Button
          onClick={handleSubmitComment}
          disabled={isSubmitting || !newComment.trim()}
          size="icon"
          className="h-8 w-8 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow"
          aria-label="Add Comment"
        >
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
} 