import { handleError } from "../../../utils/helpers";

async function findCurrentTicketStage(ticketId: string) {
  const ticket = await prisma.ticket.findUnique({
    where: { id: ticketId },
    select: { id: true, currentStageId: true },
  });

  if (!ticket.currentStageId) {
    console.log(`no current stage set for ticket ${ticketId}`);
    return null;
  }

  // Find the ticket stage that corresponds to the current pipeline stage
  const query = `select * from ticket_stages where ticket_id = '${ticketId}' and pipeline_stage_id = '${ticket.currentStageId}'`;

  const result = await prisma.$queryRawUnsafe(query);
  if (result && result[0]) {
    console.log(`current ticket stage ${result[0].id} found for ticket ${ticketId} with pipeline stage ${ticket.currentStageId}`);
  } else {
    console.log(`no ticket stage found for ticket ${ticketId} with pipeline stage ${ticket.currentStageId}`);
  }
  return result[0];
}

async function isStateChange(ticketId: string, ticketStageId: string) {
  const ticket = await prisma.ticket.findUnique({
    where: { id: ticketId },
    select: { id: true, currentStageId: true },
  });

  // Compare pipeline stage IDs directly
  return !!(ticket.currentStageId && ticket.currentStageId !== ticketStageId);
}

// Create a log entry for a ticket stage change
async function createTicketStageChangeLog(
  ticketId: string,
  ticketStageId: string,
  userId: string
) {
  const ticket = await prisma.ticket.findUnique({
    where: { id: ticketId },
    select: { id: true, currentStageId: true },
  });

  // Always create a new log entry
  return await prisma.ticketStageChangeLog.create({
    data: {
      ticketId,
      fromStage: ticket.currentStageId,
      toStage: ticketStageId,
      createdBy: userId,
    },
  });
}

// Helper function to update a single ticket (used by both single and bulk update)
async function handleTicketUpdate({
  ticketId,
  ticketStageId,
  otherFields,
  userId,
}: {
  ticketId: string;
  ticketStageId: string;
  otherFields: any;
  userId: string;
}) {
  let ticketStageChangeLog = null;
  if (await isStateChange(ticketId, ticketStageId)) {
    const log = await createTicketStageChangeLog(
      ticketId,
      ticketStageId,
      userId
    );
    ticketStageChangeLog = {
      id: log.id,
      fromStage: log.fromStage,
      toStage: log.toStage,
      createdBy: log.createdBy,
      createdAt: log.createdAt,
    };
  }

  await prisma.ticket.update({
    where: { id: ticketId },
    data: {
      ...otherFields,
      // ...(ticketStageId && { currentStageId: ticketStageId }),
      currentStageId: ticketStageId,
      updatedBy: userId,
    },
  });

  const updatedFields = { ...otherFields, updatedBy: userId };
  return {
    status: "success",
    ticketId,
    updatedFields,
    ticketStageChangeLog,
  };
}

export const ticketUpdate = async (req: any, res: any) => {
  const { ticketId, ticketStageId, createdBy, updatedBy, ...otherFields } = req.body;
  const userId = createdBy || updatedBy || "system";

  try {
    const result = await handleTicketUpdate({
      ticketId,
      ticketStageId,
      otherFields,
      userId,
    });
    
    return res.status(200).json({
      success: true,
      updatedFields: result.updatedFields,
      ticketStageChangeLog: result.ticketStageChangeLog,
    });
  } catch (error) {
    return handleError(res, error);
  }
};

// Bulk update for multiple tickets
export const bulkUpdateTickets = async (req: any, res: any) => {
  const updates = req.body.tickets; // Expecting an array of { ticketId, ticketStageId, createdBy, updatedBy, ...otherFields }

  const results = [];

  for (const update of updates) {
    const { ticketId, ticketStageId, createdBy, updatedBy, ...otherFields } = update;
    
    try {
      const result = await handleTicketUpdate({
        ticketId,
        ticketStageId,
        otherFields,
        userId: createdBy || updatedBy || "system",
      });
      results.push(result);
    } catch (error) {
      results.push({
        status: "failed",
        ticketId,
        error: error.message,
        ticketStageChangeLog: null,
      });
    }
  }

  const allSucceeded = results.every((r) => r.status === "success");
  const allNotFound = results.every((r) => r.status === "not_found");

  return res.status(allNotFound ? 404 : allSucceeded ? 201 : 207).json({
    message: allNotFound
      ? "No tickets found."
      : allSucceeded
      ? "All tickets updated successfully."
      : "Some tickets could not be updated.",
    results,
  });
};
