"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../controllers/create");
const login_1 = require("../../utils/login");
const update_1 = require("../controllers/update");
const delete_1 = require("../controllers/delete");
const view_1 = require("../controllers/view");
const viewCm_1 = require("../controllers/viewCm");
const logout_1 = require("../controllers/logout");
const authentication_1 = require("../../middleware/authentication");
const checkPermission_1 = require("../../middleware/checkPermission");
const multer_1 = __importDefault(require("multer"));
const router = (0, express_1.Router)();
const DIR = "./src/public";
const storage = multer_1.default.diskStorage({
    destination: function (req, file, cb) {
        cb(null, DIR);
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + "-" + file.originalname);
    },
});
const upload = (0, multer_1.default)({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 },
}).single("file");
router.post("/create-user", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("USER MANAGEMENT", "create-user"), create_1.createUser);
router.post("/excel", upload, create_1.excelUsers); //user
router.post("/login", login_1.userLogin);
router.post("/logout", logout_1.logout);
router.post("/sessionlogout", logout_1.sessionlogout);
router.get("/get-all-session", view_1.viewSession);
router.get("/", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("USER MANAGEMENT", "view-user"), view_1.viewUser);
router.get("/current", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("USER MANAGEMENT", "view-user"), view_1.getCurrentUserProfile);
router.put("/update-user/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("USER MANAGEMENT", "update-user"), update_1.updateUser);
router.delete("/delete-user/:id", authentication_1.authenticate, (0, checkPermission_1.checkPermissionMiddleware)("USER MANAGEMENT", "delete-user"), delete_1.deleteUser);
router.post("/upload-profile-image", authentication_1.authenticate, upload, create_1.uploadProfileImage);
router.get("/:id/cm", 
// authenticate,
viewCm_1.getUserCm);
exports.default = router;
//# sourceMappingURL=userroutes.js.map