"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { format } from "date-fns"
import { Calendar, User, FileText, MessageCircle, Tag } from "lucide-react"
import { Ticket } from "../ticket"
import { CommentSection } from "./comment-section"
import { TagManager } from "./tag-manager"

interface TicketDetailsModalProps {
  ticket: Ticket | null
  isOpen: boolean
  onClose: () => void
  onTicketUpdated: () => void
}

export function TicketDetailsModal({ 
  ticket, 
  isOpen, 
  onClose, 
  onTicketUpdated 
}: TicketDetailsModalProps) {
  const [activeTab, setActiveTab] = useState<"details" | "comments" | "tags">("details")

  if (!ticket) return null

  const handleCommentAdded = () => {
    onTicketUpdated()
  }

  const handleTagsUpdated = () => {
    onTicketUpdated()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {ticket.title}
          </DialogTitle>
        </DialogHeader>

        {/* Ticket Info */}
        <div className="grid grid-cols-2 gap-4 mb-6">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">Owner: {ticket.owner}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">
              Created: {format(new Date(ticket.createdAt), "MMM d, yyyy")}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={`
              ${ticket.priority === "high" ? "bg-red-100 text-red-800" : 
                ticket.priority === "medium" ? "bg-yellow-100 text-yellow-800" : 
                "bg-green-100 text-green-800"}
            `}>
              Priority: {ticket.priority}
            </Badge>
          </div>
          {ticket.dueDate && (
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-600">
                Due: {format(new Date(ticket.dueDate), "MMM d, yyyy")}
              </span>
            </div>
          )}
        </div>

        {/* Tabs */}
        <div className="flex border-b mb-4">
          <button
            onClick={() => setActiveTab("details")}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
              activeTab === "details" 
                ? "border-blue-500 text-blue-600" 
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            Details
          </button>
          <button
            onClick={() => setActiveTab("comments")}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors flex items-center gap-1 ${
              activeTab === "comments" 
                ? "border-blue-500 text-blue-600" 
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            <MessageCircle className="h-4 w-4" />
            Comments ({ticket.comments.length})
          </button>
          <button
            onClick={() => setActiveTab("tags")}
            className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors flex items-center gap-1 ${
              activeTab === "tags" 
                ? "border-blue-500 text-blue-600" 
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
          >
            <Tag className="h-4 w-4" />
            Tags ({ticket.tags.length})
          </button>
        </div>

        {/* Tab Content */}
        <div className="space-y-4">
          {activeTab === "details" && (
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Description</h3>
                <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                  {ticket.description ? (
                    <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                      {ticket.description}
                    </p>
                  ) : (
                    <p className="text-gray-500 italic">No description provided</p>
                  )}
                </div>
              </div>

              {ticket.pipeline && (
                <div>
                  <h3 className="font-medium mb-2">Pipeline</h3>
                  <p className="text-gray-600">{ticket.pipeline.name}</p>
                </div>
              )}

              {ticket.stages && ticket.stages.length > 0 && (
                <div>
                  <h3 className="font-medium mb-2">Stages</h3>
                  <div className="space-y-2">
                    {ticket.stages.map((stage, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm">
                        <span className="w-4 h-4 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs">
                          {index + 1}
                        </span>
                        <span>{stage.pipelineStage?.name || "Unknown Stage"}</span>
                        {stage.assignedTo && (
                          <Badge variant="outline" className="text-xs">
                            {stage.assignedTo}
                          </Badge>
                        )}
                        {stage.dueAt && (
                          <Badge variant="secondary" className="text-xs">
                            Due: {format(new Date(stage.dueAt), "MMM d")}
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === "comments" && (
            <CommentSection
              ticketId={ticket.id}
              createdBy={ticket.createdBy || ""}
              setCommentsCount={() => {}}
              isActive={activeTab === "comments"}
            />
          )}

          {activeTab === "tags" && (
            <TagManager
              ticketId={ticket.id}
              assignedTags={ticket.tags}
              onTagsUpdated={handleTagsUpdated}
              createdBy={ticket.createdBy || ""}
            />
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
} 